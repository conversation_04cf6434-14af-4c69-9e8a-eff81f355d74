import { resolve } from "node:path";
import { defineConfig } from "vite";
import uni from "@dcloudio/vite-plugin-uni";
import AutoImport from "unplugin-auto-import/vite";
import Unocss from 'unocss/vite'

const root = process.cwd();
const pathResolve = (pathname: string) => resolve(root, ".", pathname);
// https://vitejs.dev/config/
export default defineConfig({
  server: {
    proxy: {
      "/api": {
        changeOrigin: true,
        // target: 'http://101.133.141.54:8001',
        target: "http://101.133.141.54:8080",
        rewrite: (path) => path.replace(/^\/api/, "/api"),
        secure: true,
      },
    },
  },
  plugins: [
    uni(),
    Unocss(),
    AutoImport({
      imports: [
        "vue",
        "pinia",
        {
          "nutui-uniapp/composables": [
            // 在这里添加需要自动导入的API
            "useToast",
          ],
        },
      ],
      dts: "src/auto-imports.d.ts",
    }),
  ],
  resolve: {
    alias: [
      {
        find: /@\//,
        replacement: `${pathResolve("src")}/`,
      },
      // #/xxxx => types/xxxx
      {
        find: /#\//,
        replacement: `${pathResolve("types")}/`,
      },
    ],
  },
  // css: {
  // 	preprocessorOptions: {
  // 		scss: {
  // 			additionalData:
  // 				"@import '@/custom_theme.scss';@import 'nutui-uniapp/styles/variables.scss';",
  // 		},
  // 	},
  // },
});
