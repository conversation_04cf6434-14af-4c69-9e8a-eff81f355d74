declare namespace Request {
  interface httpType {
    url: string; //接口地址
    data?: any; //向后端传递的数据
    params?: any; //query参数
    method?:
      | "OPTIONS"
      | "GET"
      | "HEAD"
      | "POST"
      | "PUT"
      | "DELETE"
      | "TRACE"
      | "CONNECT"; //接口类型
    // 是否显示请求成功和失败的提示
    showToast?: boolean;
    isLoading?: boolean;
    headers?: Record<string, string>;
    okmsg?: string; // 发送成功消息文本
    errmsg?: string; // 发送失败消息文本
    // 是否需要鉴权
    isAuth?: boolean;
  }
  // 返回数据格式
  interface resType {
    cookies: Array<any>;
    data: resDataType;
    errMsg: string;
    header: any;
    statusCode: number;
  }
  // 接口返回数据格式
  interface resDataType {
    code: number;
    data: any;
    msg: string;
  }

  interface showToastMsgType {
    showToast: boolean;
    isSuccess: boolean;
    msg?: string;
  }

  interface httpUpload {
    url: string; //接口地址
    header?: Record<string, string>;
    formData?: Record<string, string>;
    filePath: string;
    name: string;
  }

  interface requestMap extends httpType {}
}
