export type AuditStatus = "stage" | "init" | "approve" | "refuse";

export type AuthStatus = "init" | "success" | "fail" | "wait";

export interface SwiperItem {
  id: string;
  url: string;
  sort: number;
  createTime: string;
}

export interface UserState {
  userId: string;
  type: string;
  username: string;
  phone: string;
  nickname: string;
  avatar: string;
  education: number | null | undefined;
  sex: number | null | undefined;
  enterpriseName: number | null | undefined;
  audit: AuditStatus | null; // 审核状态
  storeReasons: string | null; // 审核拒绝原因
  auth: AuthStatus | null; // 用户认证状态
  swiperList: SwiperItem[];
  swiperTime: number;
  memberReasons: string | null; // 审核拒绝原因 用户
  operationAudit: any;
}

export interface UserInfo {
  name: string;
  username: string;
  phone: string;
  nickname: string;
  id: string;
  education?: number;
}
