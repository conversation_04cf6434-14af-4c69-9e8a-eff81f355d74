export type CreateMapParamsExtend =
  | Record<string, any>
  | string
  | Array<any>
  | undefined
  | number
  | boolean
  | null;

export type CreateMapParams = [any, string, string, CreateMapParamsExtend?];

// 返回值
export interface CreateMapReturnValue {
  value: any;
  label: string;
  color: string;
  text: string;
  extend?: CreateMapParamsExtend;
}

// 扩展 CreateMapReturnValue
export interface ExtendCreateMapReturnValue extends CreateMapReturnValue {
  // 是否查询成功
  success: boolean;
}

export type CreateMapReturn = CreateMapReturnValue[];
