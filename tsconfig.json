{
	"extends": "@vue/tsconfig/tsconfig.json",
	"compilerOptions": {
		"moduleResolution": "node",
		"sourceMap": true,
		"baseUrl": ".",
		"paths": {
			"@/*": ["./src/*"],
			"#/*": ["./types/*"]
		},
		"lib": ["esnext", "dom"],
		"types": [
			"@dcloudio/types",
			"wot-design-uni/global.d.ts",
			"@uni-helper/uni-app-types",
			"nutui-uniapp/global.d.ts",
			"node"
		],

	},
	"include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.d.ts"],
	"vueCompilerOptions": {
		// 调整 Volar（Vue 语言服务工具）解析行为，用于为 uni-app 组件提供 TypeScript 类型
		"plugins": ["@uni-helper/uni-app-types/volar-plugin"]
	}
}
