<script setup lang="ts">
import { back } from "@/router/topage";
interface Props {
  title: string;
  // 是否透明背景
  transparent?: boolean;
  // 标题位置
  titlePosition?: "left" | "center";
  safe?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  transparent: true,
  titlePosition: "center",
  safe: true,
});

const customStyle = computed(() => {
  return props.transparent ? "background-color: transparent !important" : "";
});

const titleClass = computed(() => {
  if (props.titlePosition === "left") {
    return "left";
  }
});
</script>

<template>
  <view class="common-container common-bg2">
    <view class="common-navbar">
      <wd-navbar
        :custom-style="customStyle"
        custom-class="ly-info-navbar"
        placeholder
        safeAreaInsetTop
        :bordered="false"
        left-arrow
        @click-left="back"
      >
        <template #title>
          <view
            class="common-navbar-title common-bg2-title"
            :class="titleClass"
          >
            {{ title }}
          </view>
        </template>
      </wd-navbar>
    </view>
    <view class="common-box">
      <slot></slot>
    </view>
    <nut-safe-area v-if="safe" position="bottom"></nut-safe-area>
    <zk-env-watermark />
  </view>
</template>

<style scoped lang="scss"></style>
