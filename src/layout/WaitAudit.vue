<script setup lang="ts">
import { useLoginStore } from '@/store/login'

const loginStore = useLoginStore()
function test() {
  loginStore.setToken('a0336bfa-d461-4fd0-ba84-5ff78898b59e')
}
</script>

<template>
  <view class="NotLogin">
    <view class="NotLogin-box">
      <nut-empty
        description="您的个人信息正在审核，暂无法使用此功能。请耐心等待，审核通过后即可正常使用。"
      />
    </view>
  </view>
</template>

<style scoped lang="scss">
.NotLogin {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding-top: 25%;

  &-image {
    width: 400rpx;
  }
  &-box {
    //padding-bottom: 160rpx;
    box-sizing: border-box;
  }
  &-button {
    margin-top: 40rpx;
    //min-width: 300rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
