<script setup lang="ts">
import { useLoginStore } from "@/store/login";
import { goEditAuthUser } from "@/router/topage";

const loginStore = useLoginStore();
function test() {
  loginStore.setToken("a0336bfa-d461-4fd0-ba84-5ff78898b59e");
}
</script>

<template>
  <view class="NotLogin">
    <view class="NotLogin-box">
      <nut-empty
        description="您尚未完成个人信息认证，部分功能暂不可用。请先完成认证，以正常使用本服务。"
      >
        <view class="NotLogin-button">
          <nut-button @click="goEditAuthUser()" type="primary">
            立即认证
          </nut-button>
        </view>
      </nut-empty>
    </view>
  </view>
</template>

<style scoped lang="scss">
.NotLogin {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding-top: 25%;

  &-image {
    width: 400rpx;
  }
  &-box {
    //padding-bottom: 160rpx;
    box-sizing: border-box;
  }
  &-button {
    margin-top: 40rpx;
    //min-width: 300rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
