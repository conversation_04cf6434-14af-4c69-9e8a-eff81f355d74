<script setup lang="ts">
import { back } from "@/router/topage";
interface Props {
  title: string;
  // 是否透明背景
  transparent?: boolean | string;
  // 标题位置
  titlePosition?: "left" | "center";

  loading?: boolean;

  safe?: boolean;

  color?: string;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  transparent: true,
  titlePosition: "center",
  loading: false,
  safe: true,
});

const customStyle = computed(() => {
  // 先判断 transparent 是布尔值还是 string
  if (typeof props.transparent === "string") {
    return `background-color:${props.transparent} !important`;
  }
  return props.transparent ? "background-color: transparent !important" : "";
});

const titleClass = computed(() => {
  if (props.titlePosition === "left") {
    return "left";
  }
});

const navbarStyle = computed(() => {
  if (props.color) {
    return {
      color: props.color,
    };
  }
  return {};
});
</script>

<template>
  <view class="common-container common-bg">
    <view class="common-navbar">
      <wd-navbar
        :custom-style="customStyle"
        placeholder
        safeAreaInsetTop
        :bordered="false"
        left-arrow
        @click-left="back"
      >
        <template #title>
          <view
            :style="navbarStyle"
            class="common-navbar-title"
            :class="titleClass"
          >
            {{ title }}
          </view>
        </template>
      </wd-navbar>
    </view>
    <view class="common-box">
      <slot></slot>
      <zk-loading center :loading="loading"></zk-loading>
    </view>
    <slot name="bottom"></slot>
    <nut-safe-area v-if="safe" position="bottom"></nut-safe-area>
    <wd-toast />
    <zk-env-watermark />
  </view>
</template>

<style scoped lang="scss"></style>
