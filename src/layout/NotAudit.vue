<script setup lang="ts">
import { goEditAuthUser } from "@/router/topage";
import { useUserStore } from "@/store/user";

const userStore = useUserStore();
</script>

<template>
  <view class="NotLogin">
    <view class="NotLogin-box">
      <nut-empty
        :description="`您的个人信息认证未通过，无法正常使用本服务。失败原因：${userStore.memberReasons}`"
      >
        <view class="NotLogin-button">
          <nut-button @click="goEditAuthUser()" type="primary">
            重新认证
          </nut-button>
        </view>
      </nut-empty>
    </view>
  </view>
</template>

<style scoped lang="scss">
.NotLogin {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding-top: 25%;

  &-image {
    width: 400rpx;
  }
  &-box {
    //padding-bottom: 160rpx;
    box-sizing: border-box;
  }
  &-button {
    margin-top: 40rpx;
    //min-width: 300rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
