<script setup lang="ts">
import { useLoginStore } from "@/store/login";
import { goLogin } from "@/router/topage";

const loginStore = useLoginStore();
function test() {
  loginStore.setToken("a0336bfa-d461-4fd0-ba84-5ff78898b59e");
}
</script>

<template>
  <view class="NotLogin">
    <view class="NotLogin-box">
      <nut-empty description="还没有登录哦">
        <view class="NotLogin-button">
          <nut-button block @click="goLogin()" type="primary">
            立即登录
          </nut-button>
        </view>
      </nut-empty>
    </view>
  </view>
</template>

<style scoped lang="scss">
.NotLogin {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding-top: 25%;

  &-image {
    width: 400rpx;
  }
  &-box {
    //padding-bottom: 160rpx;
    box-sizing: border-box;
  }
  &-button {
    margin-top: 40rpx;
    width: 300rpx;
  }
}
</style>
