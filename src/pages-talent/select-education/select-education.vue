<template>
  <ly-default title="选择申请学历" title-position="left">
    <view class="common-com container common-bg4">
      <view class="container-title common-pd">
        <zk-text size="44" color="#333">请选择申请的学历</zk-text>
      </view>
      <view class="common-pd container-radio">
        <zk-radio-one v-model:value="edu" :options="options"></zk-radio-one>
      </view>
      <view class="container-action common-pd">
        <nut-button @click="methods.onsubmit" type="primary" block
          >确认学历</nut-button
        >
      </view>
    </view>
  </ly-default>
</template>

<script setup lang="ts">
import { usePathData } from "@/hook/usePathData";
import { back } from "@/router/topage";
import { first, get, size } from "lodash-es";
const edu = ref();
const { eventChannel, eventData } = usePathData("pathData", (v) => {
  const one = first(get(unref(eventData), "options")) as any;
  console.log("111", one);
  if (one) {
    edu.value = one.value;
  }
});

const options = computed(() => {
  if (size(unref(eventData))) {
    return unref(eventData).options;
  } else {
    return [];
  }
});

const methods = {
  onsubmit() {
    if (unref(edu)) {
      eventChannel.value &
        eventChannel.value.emit("onSelectEducation", {
          edu: unref(edu),
        });
      back();
    }
  },
};
</script>

<style scoped lang="scss">
.container {
  // 样式区域
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  &-radio {
    flex: 1;
    margin-top: 120rpx;
  }
  &-title {
    margin-top: 110rpx;
    display: flex;
    justify-content: center;
  }
  &-action {
    padding-top: 14rpx;
    padding-bottom: 14rpx;
  }
}
</style>
