<template>
  <ly-default :safe="false" title="申请人才学历补贴" title-position="left">
    <view class="common-com common-pd container">
      <view>
        <zk-card :head="false">
          <zk-text size="28" color="#1677FF">
            您的申请信息已保存，请完成企业盖章并上传相关文件，方可提交申请进入审核流程
          </zk-text>
        </zk-card>
      </view>
      <nut-form ref="formRef" :model-value="state">
        <nut-form-item
          required
          label-position="top"
          label-align="left"
          prop="enterpriseAttach"
        >
          <template #label>
            <zk-text size="32" color="#333">企业审核意见</zk-text>
          </template>
          <view>
            <zk-text color="#999"
              >请先下载《企业审核表》，填写审核意见并盖章后上传</zk-text
            >
          </view>
          <zk-upload-file
            v-model:value="state.enterpriseAttach"
            title="请上传附件"
          ></zk-upload-file>
        </nut-form-item>
        <view class="container-down">
          <nut-button
            @click="methods.generate()"
            :loading="loading"
            plain
            type="primary"
            block
          >
            下载企业审核表
          </nut-button>
        </view>
      </nut-form>
    </view>

    <template #bottom>
      <view class="common-pd">
        <zk-button-action
          :current="progress"
          :options="buttonOption"
          :loadingSubmit="loadingSubmit"
        ></zk-button-action>
      </view>
    </template>
  </ly-default>
</template>

<script setup lang="ts">
import { back, goHome, goToPage } from "@/router/topage";
import {
  apiMemberGenerateEducation,
  apiMemberApplyEducation,
} from "@/api/member";
import { useApiLoading } from "@/hook/useApiLoading";
import { usePathData } from "@/hook/usePathData";
import { get } from "lodash-es";
const { eventData } = usePathData("pageData");

const state = ref({
  enterpriseAttach: "",
});
const progress = ref("1");
const { loading, reload } = useApiLoading({
  api: apiMemberGenerateEducation,
  immediate: false,
  toastLoading: true,
});
const { loading: loadingSubmit, reload: reloadSubmit } = useApiLoading({
  api: apiMemberApplyEducation,
  immediate: false,
  toastLoading: true,
});
const methods = {
  // 请求接口
  async generate() {
    reload(unref(eventData)).then((res) => {
      console.log("文档", res);
      // 拼接 url
      const url = `${get(res, "host")}${get(res, "url")}`;
      methods.downloadFile(url);
    });
  },

  // 下载文本文件
  downloadFile(url: string) {
    uni.downloadFile({
      url: url,
      success: function (res) {
        const filePath = get(res, "tempFilePath");
        uni.openDocument({
          filePath: filePath,
          showMenu: true,
          success: function (res) {
            console.log("打开文档成功");
          },
          fail: function (res) {
            uni.showToast({
              title: "打开文档失败",
              icon: "none",
            });
          },
        });
      },
    });
  },
};

const buttonOption = [
  {
    value: "1",
    cancel: () => {
      goHome();
    },
    ok: async () => {
      await reloadSubmit({
        ...unref(eventData),
        ...unref(state),
        state: "init",
      });
      goToPage(
        "/pages-talent/result/result",
        {
          status: "success",
          type: "education",
        },
        "path",
      );
    },
    // ok按钮扩展
    okExtend: {
      type: "primary",
      text: "立即提交",
    },
    cancelExtend: {
      type: "default",
      text: "稍后提交",
    },
  },
];
</script>

<style scoped lang="scss">
.container {
  // 样式区域
  &-down {
    padding: 24rpx;
  }
}
</style>
