<script setup lang="ts">
import { useRuleFormItem } from "@/hook/useFormItem";
interface Props {
  value: any;
}
const props = defineProps<Props>();
const emit = defineEmits(["update:value"]);
const [state] = useRuleFormItem<Props, keyof Props, any>(props, "value", [
  "update:value",
]);

const formRef = ref();
const formRef2 = ref();
const submit = async () => {
  const { validate } = await formRef.value!;
  const { validate: validate2 } = await formRef2.value!;
  // 同时校验两个
  const [basicV, basicV2] = await Promise.all([validate(), validate2()]);
  if (basicV?.valid && basicV2?.valid) {
    if (state.value?.idCardFrontAttach && state.value?.idCardBackAttach) {
      return Promise.resolve(unref(state));
    }
  }
  uni.showToast({
    title: "请完善信息",
    icon: "none",
  });
  return Promise.reject();
};

defineExpose({
  submit,
});
</script>

<template>
  <view>
    <nut-form ref="formRef" :model-value="state">
      <nut-form-item
        prop="idCardFrontAttach"
        required
        label-align="left"
        label-position="top"
        :rules="[{ required: true, message: '请上传' }]"
      >
        <template #label>
          <zk-text size="32" color="#333">身份证上传</zk-text>
        </template>
        <view>
          <zk-text color="#ccc">
            请上传身份证的正反面，如有多张文件请合并一起上传
          </zk-text>
        </view>
        <view class="container-id">
          <view class="container-id-item">
            <zk-upload-image
              v-model:value="state.idCardFrontAttach"
              title="身份证正面"
            ></zk-upload-image>
          </view>
          <view class="container-id-item">
            <zk-upload-image
              v-model:value="state.idCardBackAttach"
              title="身份证反面"
            ></zk-upload-image>
          </view>
        </view>
      </nut-form-item>
    </nut-form>
    <nut-form ref="formRef2" :model-value="state">
      <view>
        <view class="custom-form-card-title">
          <zk-text size="32" color="#333">基本信息</zk-text>
        </view>

        <nut-form-item
          prop="name"
          :rules="[{ required: true, message: '请输入' }]"
          required
          label="姓名"
          label-align="left"
        >
          <nut-input
            placeholderClass="custom-input-placeholder"
            placeholder="请输入姓名"
            inputClass="custom-input"
            v-model:model-value="state.name"
          ></nut-input>
        </nut-form-item>
        <nut-form-item
          :rules="[{ required: true, message: '请选择' }]"
          prop="sex"
          required
          label="性别"
          label-align="left"
        >
          <view class="custom-right">
            <nut-radio-group
              direction="horizontal"
              placeholderClass="custom-input-placeholder"
              placeholder="性别"
              v-model:model-value="state.sex"
            >
              <nut-radio label="1">男</nut-radio>
              <nut-radio label="2">女</nut-radio>
            </nut-radio-group>
          </view>
        </nut-form-item>
        <nut-form-item
          prop="nativePlace"
          required
          label="籍贯"
          label-align="left"
          :rules="[{ required: true, message: '请输入' }]"
        >
          <nut-input
            inputClass="custom-input"
            placeholderClass="custom-input-placeholder"
            placeholder="请输入"
            v-model:model-value="state.nativePlace"
          ></nut-input>
        </nut-form-item>

        <nut-form-item
          prop="birthday"
          required
          label="出生年日"
          label-align="left"
          :rules="[{ required: true, message: '请选择' }]"
        >
          <zk-date-picker
            placeholder="选择日期"
            type="date"
            value-format="YYYY-MM-DD"
            v-model:value="state.birthday"
          ></zk-date-picker>
        </nut-form-item>
        <nut-form-item
          prop="idCard"
          required
          label="身份证号"
          label-align="left"
          :rules="[{ required: true, message: '请输入' }]"
        >
          <nut-input
            inputClass="custom-input"
            placeholderClass="custom-input-placeholder"
            placeholder="请输入"
            v-model:model-value="state.idCard"
          ></nut-input>
        </nut-form-item>
        <nut-form-item
          :rules="[{ required: true, message: '请输入' }]"
          prop="nation"
          required
          label="民族"
          label-align="left"
        >
          <nut-input
            inputClass="custom-input"
            placeholderClass="custom-input-placeholder"
            placeholder="请输入"
            v-model:model-value="state.nation"
          ></nut-input>
        </nut-form-item>
        <nut-form-item
          :rules="[{ required: true, message: '请输入' }]"
          prop="memberPhone"
          label="本人电话"
          label-align="left"
        >
          <nut-input
            inputClass="custom-input"
            placeholderClass="custom-input-placeholder"
            placeholder="请输入"
            v-model:model-value="state.memberPhone"
          ></nut-input>
        </nut-form-item>
        <nut-form-item
          :rules="[{ required: true, message: '请选择' }]"
          prop="health"
          label="健康状况"
          label-align="left"
        >
          <zk-picker
            v-model:value="state.health"
            :options="[
              {
                value: 1,
                text: '健康',
              },
              {
                value: 2,
                text: '轻微疾病',
              },
              {
                value: 3,
                text: '重大疾病',
              },
            ]"
            placeholder="请选择"
          ></zk-picker>
        </nut-form-item>
        <nut-form-item
          :rules="[{ required: true, message: '请输入' }]"
          prop="memberAddress"
          label="在通住址"
          label-align="left"
        >
          <nut-input
            inputClass="custom-input"
            placeholderClass="custom-input-placeholder"
            placeholder="请输入"
            v-model:model-value="state.memberAddress"
          ></nut-input>
        </nut-form-item>
      </view>
    </nut-form>
  </view>
</template>

<style scoped lang="scss">
.container {
  // 样式区域
  &-id {
    display: flex;
    gap: 10rpx;
    &-item {
      display: inline-block;
    }
  }
}
</style>
