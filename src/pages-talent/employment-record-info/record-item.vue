<script setup lang="ts">
import { AuditStatusArray } from "@/maps/op";
import { matchMap } from "@/utils/tools";
import { get } from "lodash-es";
import type { ZkDesSchema } from "#/component";
const schemas: ZkDesSchema[] = [
  {
    field: "submitBy",
    label: "申请人",
  },
  {
    field: "submitTime",
    label: "申请时间",
  },
];
interface Props {
  item: any;
}
const props = withDefaults(defineProps<Props>(), {
  item: () => ({}),
});
const emit = defineEmits(["click"]);

const cur = computed(() => {
  return matchMap(get(props.item, "state", ""), AuditStatusArray);
});
</script>

<template>
  <view @click="emit('click')">
    <zk-card
      icon="https://tzcjlw.oss-cn-shanghai.aliyuncs.com/mini-program/card/doc.png"
      title="就业补贴申请"
    >
      <template #extra>
        <zk-tag :customColor="cur.color"> {{ cur.label }} </zk-tag>
      </template>
      <template #default>
        <zk-des :schemas="schemas" :data="item"></zk-des>
      </template>
      <template #footer>
        <view
          class="record-item-footer"
          v-if="get(cur, 'extend.show') && get(item, 'reasons')"
        >
          <zk-text :color="cur.color">
            <text> 原因: </text>
            <text>
              {{ get(item, "reasons") }}
            </text>
          </zk-text>
        </view>
      </template>
    </zk-card>
  </view>
</template>

<style scoped lang="scss">
.record-item {
  &-footer {
    padding-bottom: 20rpx;
  }
}
</style>
