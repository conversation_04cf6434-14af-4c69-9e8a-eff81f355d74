<script setup lang="ts">
import { useRuleFormItem } from "@/hook/useFormItem";
interface Props {
  value: any;
}
const props = defineProps<Props>();
const emit = defineEmits(["update:value"]);
const [state] = useRuleFormItem<Props, keyof Props, any>(props, "value", [
  "update:value",
]);

const formRef = ref();
const submit = async () => {
  const { validate } = await formRef.value!;
  // 同时校验两个
  const [basicV] = await Promise.all([validate()]);
  if (basicV.valid) {
    return Promise.resolve(unref(state));
  }
  return Promise.reject();
};

defineExpose({
  submit,
});
</script>

<template>
  <view>
    <nut-form ref="formRef" :model-value="state">
      <view class="custom-form-card-title">
        <zk-text size="32" color="#333">单位信息</zk-text>
      </view>
      <nut-form-item
        prop="enterpriseName"
        required
        label="工作单位"
        label-align="left"
        :rules="[{ required: true, message: '请输入' }]"
      >
        <nut-input
          inputClass="custom-input"
          placeholderClass="custom-input-placeholder"
          placeholder="请输入"
          v-model:model-value="state.enterpriseName"
        ></nut-input>
      </nut-form-item>
      <nut-form-item
        prop="enterpriseRegisterAddress"
        required
        label="单位注册地"
        label-align="left"
        :rules="[{ required: true, message: '请输入' }]"
      >
        <nut-input
          inputClass="custom-input"
          placeholderClass="custom-input-placeholder"
          placeholder="请输入"
          v-model:model-value="state.enterpriseRegisterAddress"
        ></nut-input>
      </nut-form-item>
      <nut-form-item
        prop="enterpriseContact"
        required
        label="单位联系人"
        label-align="left"
        :rules="[{ required: true, message: '请输入' }]"
      >
        <nut-input
          inputClass="custom-input"
          placeholderClass="custom-input-placeholder"
          placeholder="请输入"
          v-model:model-value="state.enterpriseContact"
        ></nut-input>
      </nut-form-item>
      <nut-form-item
        prop="enterprisePhone"
        required
        label="联系电话"
        label-align="left"
        :rules="[{ required: true, message: '请输入' }]"
      >
        <nut-input
          inputClass="custom-input"
          placeholderClass="custom-input-placeholder"
          placeholder="请输入"
          v-model:model-value="state.enterprisePhone"
        ></nut-input>
      </nut-form-item>
      <nut-form-item
        prop="creditCode"
        required
        label="统一社会信用代码"
        label-align="left"
        label-width="250rpx"
        :rules="[{ required: true, message: '请输入' }]"
      >
        <nut-input
          inputClass="custom-input"
          placeholderClass="custom-input-placeholder"
          placeholder="请输入"
          v-model:model-value="state.creditCode"
        ></nut-input>
      </nut-form-item>
      <nut-form-item
        prop="enterpriseAddress"
        required
        label="单位地址"
        label-align="left"
        :rules="[{ required: true, message: '请输入' }]"
      >
        <nut-input
          inputClass="custom-input"
          placeholderClass="custom-input-placeholder"
          placeholder="请输入"
          v-model:model-value="state.enterpriseAddress"
        ></nut-input>
      </nut-form-item>
    </nut-form>
    <nut-form ref="formRef2" :model-value="state">
      <nut-form-item
        prop="laborContract"
        required
        label-align="left"
        label-position="top"
        :rules="[{ required: true, message: '请上传' }]"
      >
        <template #label>
          <zk-text size="32" color="#333">与单位签订的劳动合同</zk-text>
        </template>
        <zk-upload-file
          title="请上传劳动合同"
          v-model:value="state.laborContract"
        ></zk-upload-file>
      </nut-form-item>
    </nut-form>
    <nut-form ref="formRef2" :model-value="state">
      <nut-form-item
        prop="businessLicense"
        required
        label-align="left"
        label-position="top"
        :rules="[{ required: true, message: '请上传' }]"
      >
        <template #label>
          <zk-text size="32" color="#333">单位营业执照</zk-text>
        </template>
        <zk-upload-file
          title="上传附件"
          v-model:value="state.businessLicense"
        ></zk-upload-file>
      </nut-form-item>
    </nut-form>
    <nut-form ref="formRef2" :model-value="state">
      <nut-form-item
        prop="jobAttach"
        required
        label-align="left"
        label-position="top"
        :rules="[{ required: true, message: '请上传' }]"
      >
        <template #label>
          <zk-text size="32" color="#333">在职证明</zk-text>
        </template>
        <zk-upload-file
          title="上传附件"
          v-model:value="state.jobAttach"
        ></zk-upload-file>
      </nut-form-item>
    </nut-form>
  </view>
</template>

<style scoped lang="scss">
.container {
  // 样式区域
  &-id {
    display: flex;
    gap: 10rpx;
    &-item {
      display: inline-block;
    }
  }
}
</style>
