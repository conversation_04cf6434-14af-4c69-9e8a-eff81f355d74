<script setup lang="ts">
import { useRuleFormItem } from "@/hook/useFormItem";
interface Props {
  value: any;
}
const props = defineProps<Props>();
const emit = defineEmits(["update:value"]);
const [state] = useRuleFormItem<Props, keyof Props, any>(props, "value", [
  "update:value",
]);

const formRef = ref();
const formRef2 = ref();
const submit = async () => {
  const { validate } = await formRef.value!;
  const { validate: validate2 } = await formRef2.value!;
  // 同时校验两个
  const [basicV, basicV2] = await Promise.all([validate(), validate2()]);
  if (basicV.valid && basicV2.valid) {
    return Promise.resolve(unref(state));
  }
  return Promise.reject();
};

defineExpose({
  submit,
});
</script>

<template>
  <view>
    <nut-form ref="formRef" :model-value="state">
      <view class="custom-form-card-title">
        <zk-text size="32" color="#333">社保信息</zk-text>
      </view>

      <nut-form-item
        prop="socialBaySecurityDate"
        required
        label="在通州湾社保起缴年月"
        label-width="280rpx"
        label-align="left"
        :rules="[{ required: true, message: '请选择' }]"
      >
        <zk-date-picker
          placeholder="选择日期"
          type="date"
          value-format="YYYY-MM-DD"
          v-model:value="state.socialBaySecurityDate"
        ></zk-date-picker>
      </nut-form-item>
      <nut-form-item
        prop="isSocialSecurity"
        required
        label="截止现在在通州湾连续参保未中断"
        label-width="280rpx"
        label-align="left"
        :rules="[{ required: true, message: '请选择' }]"
      >
        <view class="custom-right">
          <nut-radio-group
            direction="horizontal"
            placeholderClass="custom-input-placeholder"
            v-model:model-value="state.isSocialSecurity"
          >
            <nut-radio :label="1">是</nut-radio>
            <nut-radio :label="0">否</nut-radio>
          </nut-radio-group>
        </view>
      </nut-form-item>
      <nut-form-item
        prop="socialBaySecurityCount"
        required
        label="在通州湾社保缴纳时长(月)"
        label-align="left"
        label-width="280rpx"
        :rules="[{ required: true, message: '请输入' }]"
      >
        <view class="custom-right">
          <nut-input-number
            inputClass="custom-input"
            placeholderClass="custom-input-placeholder"
            placeholder="请输入"
            v-model:model-value="state.socialBaySecurityCount"
          ></nut-input-number>
        </view>
      </nut-form-item>
      <nut-form-item
        prop="bankName"
        required
        label="开户银行名称"
        label-align="left"
        label-width="280rpx"
        :rules="[{ required: true, message: '请输入' }]"
      >
        <nut-input
          inputClass="custom-input"
          placeholderClass="custom-input-placeholder"
          placeholder="请输入"
          v-model:model-value="state.bankName"
        ></nut-input>
      </nut-form-item>
      <nut-form-item
        prop="bankCard"
        required
        label="银行卡号"
        label-align="left"
        :rules="[{ required: true, message: '请输入' }]"
      >
        <nut-input
          inputClass="custom-input"
          placeholderClass="custom-input-placeholder"
          placeholder="请输入"
          v-model:model-value="state.bankCard"
        ></nut-input>
      </nut-form-item>
    </nut-form>
    <nut-form ref="formRef2" :model-value="state">
      <nut-form-item
        prop="socialSecurityAttach"
        required
        label-align="left"
        label-position="top"
        :rules="[{ required: true, message: '请输入' }]"
      >
        <template #label>
          <zk-text size="32" color="#333">缴纳社保记录</zk-text>
        </template>
        <view>
          <zk-text color="#ccc">
            缴纳社保记录需提供在通州湾起缴至今的记录(2021年11月（含）以后)
          </zk-text>
        </view>
        <zk-upload-file
          title="请上传社保附件"
          v-model:value="state.socialSecurityAttach"
        ></zk-upload-file>
      </nut-form-item>
    </nut-form>
  </view>
</template>

<style scoped lang="scss">
.container {
  // 样式区域
  &-id {
    display: flex;
    gap: 10rpx;
    &-item {
      display: inline-block;
    }
  }
}
</style>
