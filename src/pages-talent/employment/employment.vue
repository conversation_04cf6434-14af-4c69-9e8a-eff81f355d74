<template>
  <ly-default :safe="false" title="申请人才就业补贴" title-position="left">
    <view class="common-com common-pd container">
      <view>
        <zk-steps :current="progress" :options="progressOptions"></zk-steps>
      </view>
      <view>
        <BasicForm
          v-if="progress === '1'"
          v-model:value="state"
          ref="basicFormRef"
        ></BasicForm>
        <!--        <Education-->
        <!--          v-else-if="progress === '2'"-->
        <!--          v-model:value="state"-->
        <!--          ref="educationRef"-->
        <!--        ></Education>-->
        <!--        大专硕士博士-->
        <SocialForm1
          v-else-if="progress === '3'"
          v-model:value="state"
          ref="socialForm1Ref"
        ></SocialForm1>
        <JobForm
          v-else-if="progress === '4'"
          v-model:value="state"
          ref="jobFormRef"
        ></JobForm>
      </view>
    </view>
    <zk-affirm
      title="填写前须知"
      :text="noticeTipsText"
      :open="noticeTip"
      @on-ok="methods.goToSelectEducation()"
      @on-cancel="back()"
      control
    ></zk-affirm>
    <zk-affirm
      title="承诺书"
      :text="educationSubsidyTipsText"
      :open="educationSubsidyTip"
      @on-ok="methods.ok()"
      @on-cancel="educationSubsidyTip = false"
      control
    ></zk-affirm>

    <template #bottom>
      <view class="common-pd">
        <zk-button-action
          :loading="loading"
          :current="progress"
          :options="buttonOption"
        ></zk-button-action>
      </view>
    </template>
  </ly-default>
</template>

<script setup lang="ts">
import { noticeTipsText } from "./tipsData";
import { back, goToPage } from "@/router/topage";
import { progressOptions } from "./optData";
import BasicForm from "./basicFrom.vue";
// import Education from "./eduForm.vue";
import SocialForm1 from "./socialForm1.vue";
import JobForm from "./jobForm.vue";
import { educationSubsidyTipsText } from "@/config/tipsData";
import { apiMemberApplyEmployment } from "@/api/member";
import { useApiLoading } from "@/hook/useApiLoading";

const education = ref();

const basicFormRef = ref();
// const educationRef = ref();
const socialForm1Ref = ref();
const jobFormRef = ref();
const progress = ref("1");
const state = ref({
  name: "",
});
const { loading, reload } = useApiLoading({
  api: apiMemberApplyEmployment,
  immediate: false,
  toastLoading: true,
});
const noticeTip = computed(() => {
  return !unref(education);
});
const educationSubsidyTip = ref(false);

const methods = {
  // 去选择人才学历
  goToSelectEducation() {
    uni.navigateTo({
      url: "/pages-talent/select-education/select-education",
      events: {
        onSelectEducation: (data: any) => {
          console.log("我的选择", data);
          education.value = data.edu;
        },
      },
      success: ({ eventChannel }) => {
        eventChannel.emit("pathData", {
          options: [
            {
              value: 2,
              label: "全日制本科生",
            },
            {
              value: 3,
              label: "全日制硕士生",
            },
            {
              value: 4,
              label: "全日制博士生",
            },
          ],
        });
      },
    });
  },

  // 用户已同意承诺 请求接口
  async ok() {
    const params = {
      ...unref(state),
      education: unref(education),
      state: "stage",
    };
    // 请求接口
    reload(params).then((res) => {
      goToPage("/pages-talent/employment-opinion/employment-opinion", {
        ...params,
        id: res,
      });
    });
  },
};

const buttonOption = [
  {
    value: "1",
    cancel: () => {
      back();
    },
    ok: async () => {
      await basicFormRef.value?.submit();
      progress.value = "3";
    },
    // ok按钮扩展
    okExtend: {
      type: "primary",
      text: "下一步",
    },
    cancelExtend: {
      type: "default",
      text: "取消",
    },
  },
  // {
  //   value: "2",
  //   cancel: () => {
  //     progress.value = "1";
  //   },
  //   ok: async () => {
  //     console.log("ok");
  //     await educationRef.value?.submit();
  //     progress.value = "3";
  //   },
  //   // ok按钮扩展
  //   okExtend: {
  //     type: "primary",
  //     text: "下一步",
  //   },
  //   cancelExtend: {
  //     type: "default",
  //     text: "上一步",
  //   },
  // },
  {
    value: "3",
    cancel: () => {
      progress.value = "1";
    },
    ok: async () => {
      await socialForm1Ref.value?.submit();
      progress.value = "4";
    },
    // ok按钮扩展
    okExtend: {
      type: "primary",
      text: "下一步",
    },
    cancelExtend: {
      type: "default",
      text: "上一步",
    },
  },
  {
    value: "4",
    cancel: () => {
      progress.value = "3";
    },
    ok: async () => {
      await jobFormRef.value?.submit();
      educationSubsidyTip.value = true;
    },
    // ok按钮扩展
    okExtend: {
      type: "primary",
      text: "提交",
    },
    cancelExtend: {
      type: "primary",
      text: "上一步",
    },
  },
];
</script>

<style scoped lang="scss">
.container {
  // 样式区域
  &-title {
    padding: 24px;
  }
}
</style>
