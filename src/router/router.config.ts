import queryString from "query-string";

export enum Pages {
  Index = "/pages/index/index",
  Login = "/pages/login/login",
  NoPermission = "/pages/403/index", // 没有权限
  About = "/pages/about/about", // 404
  Subscribe = "/pages/subscribe/subscribe", // 订阅
  Webcontent = "/pages/webcontent/webcontent", // 内容
  ConsultList = "/pages/consult-list/consult-list", // 咨询列表
  ConsultInfo = "/pages/consult-info/consult-info", // 咨询详情
  GuideInfo = "/pages/guide-info/guide-info", // 指南详情
}

// 路由白名单 未登录可访问的页面
export const routeWhiteList: string[] = [
  Pages.About,
  Pages.Login,
  Pages.Index,
  Pages.NoPermission,
  Pages.Subscribe,
  Pages.Webcontent,
  Pages.ConsultList,
  Pages.ConsultInfo,
  Pages.GuideInfo,
];

// openId 白名单
export const openIdWhiteList: string[] = [];

// 权限路由 控制页面访问权限 有次页面的权限码和页面的路径
const routesPermission: Array<Array<number | string>> = [
  // [Pages.QueryUser, 7, 8],
  // [Pages.Examine, 7, 8],
];

// 从权限路由中获取权限码 传入页面路径 和 权限码 返回是否有权限
export function getPermission(path: string, permissionCode: number) {
  console.log(import.meta.env.DEV, path, permissionCode);
  if (import.meta.env.DEV) return true;
  const permission = routesPermission.find((item) => item[0] === path);
  if (!permission) return true;
  return permission.includes(permissionCode);
}

// 判断是否是openId 白名单内
export function getPermissionOpenId(openId: string): boolean {
  const permission = openIdWhiteList.includes(openId);
  return permission;
}

// 判断路由是否是白名单内
export function getPermissionRoute(path: string): boolean {
  const { url } = queryString.parseUrl(path);
  console.log("queryUrl.url", url);
  const normalizedUrl = url.startsWith("/") ? url : `/${url}`;
  const permission = routeWhiteList.includes(normalizedUrl);
  return permission;
}
