/**
 * @description 对象转url参数
 * @param data {object} 对象
 * @param isPrefix {Boolean} ,是否自动加上"?"
 * @param arrayFormat {string}  规则 indices|brackets|repeat|comma
 */
export function queryParams(
  data: any = {},
  isPrefix = true,
  arrayFormat = "brackets",
) {
  const prefix = isPrefix ? "?" : "";
  const _result = [];
  if (["indices", "brackets", "repeat", "comma"].indexOf(arrayFormat) == -1)
    arrayFormat = "brackets";
  for (const key in data) {
    const value = data[key];
    // 去掉为空的参数
    if (["", undefined, null].indexOf(value) >= 0) {
      continue;
    }
    // 如果值为数组，另行处理
    if (value.constructor === Array) {
      // e.g. {ids: [1, 2, 3]}
      switch (arrayFormat) {
        case "indices":
          // 结果: ids[0]=1&ids[1]=2&ids[2]=3
          for (let i = 0; i < value.length; i++) {
            _result.push(`${key}[${i}]=${value[i]}`);
          }
          break;
        case "brackets":
          // 结果: ids[]=1&ids[]=2&ids[]=3
          value.forEach((_value) => {
            _result.push(`${key}[]=${_value}`);
          });
          break;
        case "repeat":
          // 结果: ids=1&ids=2&ids=3
          value.forEach((_value) => {
            _result.push(`${key}=${_value}`);
          });
          break;
        case "comma":
          // 结果: ids=1,2,3
          let commaStr = "";
          value.forEach((_value) => {
            commaStr += (commaStr ? "," : "") + _value;
          });
          _result.push(`${key}=${commaStr}`);
          break;
        default:
          value.forEach((_value) => {
            _result.push(`${key}[]=${_value}`);
          });
      }
    } else {
      _result.push(`${key}=${value}`);
    }
  }
  return _result.length ? prefix + _result.join("&") : "";
}

export function addRootPath(url: string) {
  return url[0] === "/" ? url : `/${url}`;
}

export function mixinParam(url: string, params: any) {
  url = url && addRootPath(url);

  // 使用正则匹配，主要依据是判断是否有"/","?","="等，如“/page/index/index?name=mary"
  // 如果有url中有get参数，转换后无需带上"?"
  let query = "";
  if (/.*\/.*\?.*=.*/.test(url)) {
    // object对象转为get类型的参数
    query = queryParams(params, false);
    // 因为已有get参数,所以后面拼接的参数需要带上"&"隔开
    return (url += `&${query}`);
  }
  // 直接拼接参数，因为此处url中没有后面的query参数，也就没有"?/&"之类的符号
  query = queryParams(params);
  return (url += query);
}

function extractPathFromURL(url: string): string {
  try {
    const pathRegex = /\/\/[^/]+([^?]+)/;
    const match = url.match(pathRegex);
    if (match && match[1]) {
      return match[1];
    } else {
      return ""; // 或者返回一个默认的路径，或者抛出异常，取决于你的需求
    }
  } catch (error) {
    // 处理异常情况
    return ""; // 或者返回一个默认的路径，或者抛出异常，取决于你的需求
  }
}

// 格式化url
export function formatUrl(url: string | undefined): string {
  if (!url) return "";
  return url.replace(/\?.*$/, "").replace(/\#.*$/, "").replace(/^\//, "");
}
/**
 * @description 提取url
 * @param {string} url
 */
export function extractUrl(url?: string): string {
  if (!url) return "";
  let isUrl = isValidURL(url);
  if (isUrl) {
    return extractPathFromURL(url);
  } else {
    return formatUrl(url);
  }
}

/**
 * @param str {string} 链接
 * @description  判断是否很合法链接
 * @returns
 */
export function isValidURL(str: string): boolean {
  try {
    const urlRegex =
      /^(https?:\/\/)([\da-z.-]+)\.([a-z.]{2,6})([\/\w .-]*)*\/?$/i;
    return urlRegex.test(str);
  } catch (error) {
    // 处理异常情况
    return false; // 或者抛出异常，取决于你的需求
  }
}
