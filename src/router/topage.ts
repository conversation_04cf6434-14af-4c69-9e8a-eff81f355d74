import queryString from 'query-string'

export function goUserAgreement() {
  uni.navigateTo({
    url: `/pages/webcontent/webcontent?${queryString.stringify({
      url: 'https://talents.tzwcjlw.com/user_agree.htm',
      title: '用户协议',
    })}`,
  })
}

export function goPrivacyPolicy() {
  uni.navigateTo({
    url: `/pages/webcontent/webcontent?${queryString.stringify({
      url: 'https://talents.tzwcjlw.com/user_privacy.htm',
      title: '隐私政策',
    })}`,
  })
}

export function goStoreAgreement() {
  uni.navigateTo({
    url: `/pages/webcontent/webcontent?${queryString.stringify({
      url: 'https://talents.tzwcjlw.com/store_agree.htm',
      title: '商家用户协议',
    })}`,
  })
}

export function goStorePrivacyPolicy() {
  uni.navigateTo({
    url: `/pages/webcontent/webcontent?${queryString.stringify({
      url: 'https://talents.tzwcjlw.com/store_privacy.htm',
      title: '商家隐私政策',
    })}`,
  })
}

// hospitalId hospitalName
export function goSubscribe(params: any) {
  uni.navigateTo({
    url: `/pages/subscribe/subscribe?${queryString.stringify(params)}`,
  })
}

// 不保存当前页面跳转
export function goSubscribeRecordRedirect() {
  uni.redirectTo({
    url: '/pages/order-record/order-record',
  })
}

// 不保存当前页面跳转
export function goSubscribeRecord() {
  uni.navigateTo({
    url: `/pages/order-record/order-record`,
  })
}

// 跳转到登录  isRedirect 表示是否保存当前页  true 表示不保存当前页
export function goLogin(isRedirect = false) {
  if (isRedirect) {
    uni.reLaunch({
      url: '/pages/login/login',
    })
  }
  else {
    uni.navigateTo({
      url: '/pages/login/login',
    })
  }
}

// 跳转到修改用户资料
export function goEditUser() {
  uni.navigateTo({
    url: '/pages/edit-user/edit-user',
  })
}

// 跳转到用户认证修改
export function goEditAuthUser() {
  uni.navigateTo({
    url: '/pages/auth-user/auth-user',
  })
}

export function back() {
  // 判断是否是最后一页
  let pages = getCurrentPages()
  if (pages.length <= 1) {
    uni.reLaunch({
      url: '/pages/index/index',
    })
    return
  }
  uni.navigateBack()
}

export function goToDebug() {
  uni.navigateTo({
    url: '/pages/debug/debug',
  })
}

type goToPageType = 'path' | 'event'

export function goToPage(path: string, params?: any, type?: goToPageType) {
  if (!params) {
    uni.navigateTo({
      url: path,
    })
    return
  }
  const defType = type || 'event'
  if (defType === 'event') {
    uni.navigateTo({
      url: path,
      success: ({ eventChannel }) => {
        if (params) {
          eventChannel.emit('pageData', params)
        }
      },
    })
  }
  else {
    uni.navigateTo({
      url: `${path}?${queryString.stringify(params)}`,
    })
  }
}

export function goHome(name?: string) {
  if (name) {
    uni.reLaunch({
      url: `/pages/index/index?name=${name}`,
    })
  }
  else {
    uni.reLaunch({
      url: '/pages/index/index',
    })
  }
}

// 查看可使用门店
export function goTicketStore(params?: any) {
  console.log('跳转到可使用门店', queryString.stringify(params))
  uni.navigateTo({
    url: `/pages/ticket-store/ticket-store?${queryString.stringify(params)}`,
  })
}

// 核销券结果
export function goTicketResult(params?: any) {
  uni.navigateTo({
    url: `/pages/ticket-result/ticket-result?${queryString.stringify(params)}`,
  })
}
