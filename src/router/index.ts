import { useLoginStore } from "@/store/login";
import { useUserStoreWithOut } from "@/store/user";
import { routeWhiteList, Pages, getPermissionRoute } from "./router.config";
import { goLogin } from "@/router/topage";

// 通用的路由处理逻辑
function routeHandler(url: string): { isPass?: boolean; isWhite?: boolean } {
  if (getPermissionRoute(url)) return { isWhite: true };
  const { isLogin } = useLoginStore();
  if (isLogin) return { isPass: true };
  return { isPass: false };
}

export async function initRouter(path: string) {
  console.log("初始化路由");
  const { isLogin } = useLoginStore();
  const userStore = useUserStoreWithOut();
  if (isLogin) {
    // 登录需要获取api
    try {
      const res = await userStore.refreshUserInfo();
    } catch (e) {
      console.log(e);
    }
  }
  const { isPass = false, isWhite = false } = routeHandler(path);
  if (isWhite) {
    console.log("白名单路由");
  } else if (!isPass) {
    console.log("需要鉴权");
    goLogin(true);
  }
}
const list = ["navigateTo", "redirectTo", "reLaunch", "switchTab"];
// 添加拦截
export function addInterceptor() {
  list.forEach((item) => {
    uni.addInterceptor(item, {
      // 页面跳转前进行拦截, invoke根据返回值进行判断是否继续执行跳转
      async invoke(e) {
        try {
          console.log("路由", e);
          const { url, params } = e;
          const { isPass = false, isWhite = false } = routeHandler(url);
          // 如果是白名单中的路由全部放行
          console.log("isPass", isPass);
          console.log("isWhite", isWhite);
          if (isWhite) {
            console.log("白名单路由");
            return e;
          } else if (!isPass) {
            console.log("需要鉴权");
            e.url = Pages.Login;
            return e;
          }
          return e;
        } catch (e) {
          console.log(e);
          return;
        }
      },
      fail(err) {
        // 失败回调拦截
        console.log("拦截失败", err);
      },
      success(res) {
        // 成功回调拦截
        console.log("拦截成功", res);
      },
    });
  });
}
