.safe-area {
  &::after {
    content: "";
    display: block;
    // 安全底部高度
    height: env(safe-area-inset-bottom);
  }
}
.nut-button--square {
  border-radius: 8rpx !important;
}
//.nut-button--plain.nut-button--danger{
//  border-color:transparent !important;
//}

.common {
  &-container {
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
  }
  &-navbar {
    flex-shrink: 0;
    &-title {
      color: #333;
      font-size: 34rpx;
      font-weight: 400;
      &.left {
        left: 100rpx;
        position: absolute;
      }
    }
  }
  &-list {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    padding: 24rpx;
    box-sizing: border-box;
  }
  &-scroll {
    flex-grow: 1;
    overflow: auto;
    flex-shrink: 0;
    height: 0;
    box-sizing: border-box;
    position: relative;
  }
  &-empty {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin-top: -50rpx;
  }
  &-box {
    height: 0;
    flex-grow: 1;
    overflow: auto;
    box-sizing: border-box;
    position: relative;
  }
  &-com {
    height: 100%;
    position: relative;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }
  &-pd {
    padding-left: 24rpx;
    padding-right: 24rpx;
  }
  &-bg {
    .wd-navbar__arrow {
      color: #333;
    }
    &:after {
      content: "";
      position: fixed;
      top: -192rpx;
      left: -110rpx;
      width: 519rpx;
      height: 519rpx;
      border-radius: 519rpx;
      background: radial-gradient(
                      50% 50% at 50% 50%,
                      #e7f6ff 0%,
                      rgba(255, 255, 255, 0.5) 100%
      );
      z-index: -2;
    }
  }
  &-bg1{
    .wd-navbar__arrow {
      color: #333;
    }
    &-title{
      color: #333;
    }
    &:after{
      content: "";
      position: fixed;
      top: 0rpx;
      left: 0rpx;
      width: 100vw;
      height: 900rpx;
      z-index: -1;
      background: linear-gradient(180deg, #C3E0FF 17.85%, rgba(255, 255, 255, 0.00) 100%);
    }
  }
  &-bg2 {
    .wd-navbar__arrow {
      color: #fff;
    }
    &-title{
      color: #fff;
    }
    &:after{
      content: "";
      position: fixed;
      top: 0rpx;
      left: 0rpx;
      width: 100vw;
      height: 400rpx;
      z-index: -1;
      background: linear-gradient(to bottom, #1677FF 0%,#1677FF 60%, #fff 100%);
    }
  }
  &-bg3 {
    .wd-navbar__arrow {
      color: #333;
    }
    &-title{
      color: #333;
    }
    &:after{
      content: "";
      position: fixed;
      top: 0rpx;
      left: 0rpx;
      width: 100vw;
      height: 990rpx;
      z-index: -2;
      background: linear-gradient(180deg, #C3E0FF 17.85%, rgba(255, 255, 255, 0.00) 100%);
    }
    &:before{
      content: "";
      position: fixed;
      top: 0rpx;
      left: 0rpx;
      width: 100vw;
      height: 426rpx;
      background: linear-gradient(203deg, #0085FF -22.17%, rgba(255, 255, 255, 0.00) 55%);
      z-index: -1;
    }
  }

  &-bg4 {
    .wd-navbar__arrow {
      color: #333;
    }
    &-title{
      color: #333;
    }
    &:after{
      content: "";
      position: fixed;
      top: 0rpx;
      left: 0rpx;
      width: 100vw;
      height: 100vh;
      z-index: -2;
      background: #EFF3FC;
    }

  }

  &-tr{
    transition: all 0.3s ease-in-out;
  }
}

.custom{
  &-input{
    text-align: right !important;
    &-placeholder{
      text-align: right;
      color: #cccccc;
    }
  }
  &-right{
    display: flex;
    justify-content: flex-end;
  }
  &-form-card-title{
    padding: 24rpx 24rpx 14rpx 24rpx;
  }
}
