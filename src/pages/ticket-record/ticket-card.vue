<script setup lang="ts">
import { goToPage } from "@/router/topage";
import { get } from "lodash-es";
interface Props {
  item: any;
}
const props = defineProps<Props>();
const emit = defineEmits(["onGetCoupon"]);
// 时间范围展示
const showTime = computed(() => {
  return `${get(props.item, "operationCoupon.beginDate")?.replaceAll("-", ".")}-${get(props.item, "operationCoupon.endDate")?.replaceAll("-", ".")}`;
});

const methods = {
  // 查看详情
  goToInfo() {
    uni.navigateTo({
      url: "/pages/ticket-info/ticket-info",
      success: ({ eventChannel }) => {
        eventChannel.emit("pathData", toRaw(props.item));
      },
    });
  },
};
</script>

<template>
  <view class="home-ticket-card" @click="methods.goToInfo()">
    <view class="home-ticket-card-left">
      <view class="home-ticket-card-left-price">
        <view class="symbol">¥</view>
        <view>{{ get(item, "operationCoupon.price") }}</view>
      </view>
      <view class="home-ticket-card-left-tips">
        满{{ get(item, "operationCoupon.threshold") }}可用
      </view>
    </view>
    <view class="home-ticket-card-info">
      <view class="name"> {{ get(item, "operationCoupon.title") }} </view>
      <view class="time">
        {{ showTime }}
      </view>
    </view>
    <view class="home-ticket-card-action">
      <image
        class="home-ticket-card-icon-overdue"
        v-if="get(item, 'state') === 'USE'"
        src="https://tzcjlw.oss-cn-shanghai.aliyuncs.com/mini-program/used.png"
      ></image>
      <image
        class="home-ticket-card-icon-overdue"
        v-else-if="get(item, 'state') === 'OVERDUE'"
        src="https://tzcjlw.oss-cn-shanghai.aliyuncs.com/mini-program/expire.png"
      ></image>
    </view>
  </view>
</template>

<style scoped lang="scss">
.home-ticket-card {
  height: 158rpx;
  border-radius: 16rpx;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  &-icon {
    &-overdue {
      width: 100rpx;
      height: 100rpx;
    }
  }
  &-left {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 8rpx;
    width: 0;
    flex-basis: 205rpx;
    background: #c0c0cd;
    &-price {
      display: flex;
      gap: 4rpx;
      color: #fff;
      font-size: 56rpx;
      font-weight: 600;
      line-height: 56rpx;
      align-items: flex-end;
      margin-top: 10rpx;
      & .symbol {
        font-size: 20rpx;
        line-height: 20rpx;
        padding-bottom: 10rpx;
      }
    }
    &-tips {
      color: #fff;
      text-align: center;
      font-size: 24rpx;
    }
  }
  &-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex-grow: 1;
    width: 0;
    flex-shrink: 0;
    padding-left: 37rpx;
    gap: 8rpx;

    & .name {
      color: #ababb8;
      font-size: 32rpx;
      font-weight: 500;
      overflow: hidden; /* 隐藏超出的内容 */
      white-space: nowrap; /* 禁止换行 */
      text-overflow: ellipsis; /* 溢出时显示省略号 */
    }
    & .time {
      color: #ababb8;
      font-size: 24rpx;
      font-weight: 400;
    }
  }
  &-action {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-right: 24rpx;
  }
}
</style>
