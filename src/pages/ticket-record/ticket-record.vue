<script setup lang="ts">
import { useScrollView } from "@/hook/useScrollView";
import { apiMemberRecordPage } from "@/api/member";
import TicketCard from "@/pages/ticket-record/ticket-card.vue";

const state = ref({
  useState: 0,
});

// 计算属性查询参数
const params = computed(() => {
  return {
    useState: useStateMap[state.value.useState],
  };
});
const useStateMap = ["ALL", "USE", "OVERDUE"];
const {
  refresherStatus,
  onPullDownRefresh,
  onReachBottom,
  list,
  clearList,
  loading,
  reload,
  isEmpty,
} = useScrollView({
  api: apiMemberRecordPage,
  params,
});

onMounted(() => {
  watch(params, (v) => {
    clearList();
    reload();
  });
});
</script>

<template>
  <ly-default :transparent="false" title="消费券使用记录" title-position="left">
    <view class="common-com container">
      <view>
        <wd-tabs v-model="state.useState" autoLineWidth>
          <wd-tab title="全部"> </wd-tab>
          <wd-tab title="已消费"> </wd-tab>
          <wd-tab title="已过期"> </wd-tab>
        </wd-tabs>
      </view>

      <scroll-view
        :scroll-y="true"
        class="common-scroll"
        :refresher-enabled="true"
        @refresherrefresh="onPullDownRefresh"
        :refresher-triggered="refresherStatus"
        @scrolltolower="onReachBottom"
      >
        <view class="container-list common-list">
          <TicketCard v-for="item in list" :item="item" :key="item.id" />
        </view>
        <view class="common-empty" v-if="isEmpty">
          <wd-status-tip image="content" tip="暂无内容" />
        </view>
        <zk-loading :loading="loading"></zk-loading>
      </scroll-view>
    </view>
  </ly-default>
</template>

<style scoped lang="scss">
.container {
  // 样式区域
  &-list {
    margin: 0 24rpx;
  }
}
</style>
