<template>
  <ly-default title="个人设置" title-position="left">
    <view class="common-com common-pd edit-user">
      <nut-cell-group>
        <nut-cell is-link>
          <template #title>
            <view class="edit-user-title"> 头像 </view>
          </template>
          <template #link>
            <view class="edit-user-link">
              <zk-block
                style="display: inline-flex"
                open-type="chooseAvatar"
                @chooseavatar="methods.avatar"
              >
                <image
                  class="edit-user-avatar"
                  :src="userStore.avatarUrl"
                  mode="aspectFill"
                ></image>
              </zk-block>
              <nut-icon name="right"></nut-icon>
            </view>
          </template>
        </nut-cell>
        <nut-cell title="手机号">
          <template #link>
            {{ userStore.phone }}
          </template>
        </nut-cell>

        <nut-cell
          v-if="!userStore.isStore && userStore.isAuth"
          @click="methods.userRecord"
          title="我的档案"
          is-link
        >
        </nut-cell>

        <template v-if="userStore.isStore">
          <nut-cell @click="goStoreAgreement" title="商家协议" is-link />
          <nut-cell @click="goStorePrivacyPolicy" title="商家隐私" is-link />
        </template>
        <template v-else>
          <nut-cell @click="goUserAgreement" title="用户协议" is-link />
          <nut-cell @click="goPrivacyPolicy" title="用户隐私" is-link />
        </template>
      </nut-cell-group>
      <view v-if="loginStore.isLogin">
        <nut-button
          size="large"
          plain
          block
          type="danger"
          @click="methods.logout()"
        >
          退出登录
        </nut-button>
      </view>
    </view>
    <wd-message-box />
    <wd-toast />
  </ly-default>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store/user";
import { useLoginStore } from "@/store/login";
import {
  goPrivacyPolicy,
  goUserAgreement,
  goStorePrivacyPolicy,
  goStoreAgreement,
  goToPage,
} from "@/router/topage";
import { useMessage } from "wot-design-uni";
import { get } from "lodash-es";
import { apiUpload } from "@/api/file";
import { apiMemberCurrent, apiMemberCurrentLoading } from "@/api/member";
import { matchMap } from "@/utils/tools";
import { EducationArray, SexArray } from "@/maps/op";

const message = useMessage();

const userStore = useUserStore();
const loginStore = useLoginStore();

// 显示学历信息
const showEducation = computed(() => {
  if (!userStore.education) {
    return "暂无信息";
  }
  const { success, label } = matchMap(userStore.education, EducationArray);
  if (!success) {
    return "暂无信息";
  }
  return label;
});

const methods = {
  username() {
    message
      .prompt({
        title: "请输入用户姓名",
        inputValue: userStore.username,
      })
      .then((resp) => {
        console.log(resp);
        if (resp.value) {
          methods.submit({
            name: resp.value,
          });
        }
      })
      .catch(() => {
        uni.showToast({
          title: "取消操作",
          icon: "none",
        });
      });
  },
  userRecord() {
    // 跳转到档案信息
    goToPage("/pages/info-user/info-user");
  },

  async avatar(v: any) {
    console.log(v);
    const avatarUrl = get(v, "detail.avatarUrl");
    if (avatarUrl) {
      try {
        // 开始上传
        uni.showLoading({
          title: "上传中",
          duration: 0,
        });
        const res = await apiUpload(avatarUrl);
        const imgurl = `${get(res, "host")}${get(res, "url")}`;
        if (!imgurl) {
          uni.hideLoading();
          uni.showToast({
            title: "上传失败",
            icon: "none",
          });
          return;
        }
        await apiMemberCurrent({
          id: userStore.userId,
          photo: imgurl,
        });
        uni.hideLoading();
      } catch (e) {
        uni.hideLoading();
        uni.showToast({
          title: "上传失败",
          icon: "none",
        });
      } finally {
        userStore.refreshUserInfo();
      }
    }
  },
  // 提交
  async submit(v: any) {
    try {
      await apiMemberCurrentLoading({
        id: userStore.userId,
        ...v,
      });
    } catch (e) {
    } finally {
      userStore.refreshUserInfo();
    }
  },
  // 退出登录
  logout() {
    console.log("退出登录");
    message
      .confirm({
        title: "提示",
        msg: "确定退出登录吗？",
      })
      .then(() => {
        console.log("用户点击确定");
        loginStore.logout();
        uni.navigateBack();
      })
      .catch((e) => {
        console.log("用户点击取消");
      });
  },
};
</script>

<style scoped lang="scss">
.edit-user {
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  &-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
  }
  &-button {
    padding: 0;
    display: inline-flex;
    margin: 0;
    text-align: left;
    outline: none;
    line-height: normal;
    border-radius: 0;
    background-color: transparent;

    &:after {
      border: none;
    }
  }
  &-title {
    position: absolute;
    bottom: 22rpx;
  }
  &-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
