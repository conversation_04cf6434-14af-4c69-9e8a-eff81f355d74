<template>
  <ly-default title="人才资讯" title-position="left">
    <view class="common-com container">
      <scroll-view
        :scroll-y="true"
        class="common-scroll"
        :refresher-enabled="true"
        @refresherrefresh="onPullDownRefresh"
        :refresher-triggered="refresherStatus"
        @scrolltolower="onReachBottom"
      >
        <view class="common-list container-list">
          <view
            class="container-card"
            :key="index"
            v-for="(item, index) in storeList"
          >
            <home-consult-card-item :item="item"></home-consult-card-item>
          </view>
        </view>

        <view class="common-empty" v-if="isEmpty">
          <wd-status-tip image="content" tip="暂无内容" />
        </view>
        <zk-loading :loading="loading"></zk-loading>
      </scroll-view>
    </view>
  </ly-default>
</template>

<script setup lang="ts">
import { useScrollView } from "@/hook/useScrollView";
import { apiGetOpenConsult } from "@/api/open";
import HomeConsultCardItem from "@/pages/index/home/<USER>";
const {
  list: storeList,
  isEmpty,
  onPullDownRefresh,
  refresherStatus,
  onReachBottom,
  loading,
} = useScrollView({
  api: apiGetOpenConsult,
});
</script>

<style scoped lang="scss">
.container {
  &-list {
    // 样式区域
    display: flex;
    flex-direction: column;
    gap: 24rpx;
  }
  &-card {
    background: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    box-sizing: border-box;
  }
}
</style>
