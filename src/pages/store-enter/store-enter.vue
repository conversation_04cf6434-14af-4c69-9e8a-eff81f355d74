<template>
  <ly-default title="商家入驻" title-position="left">
    <view class="common-com common-pd container">
      <nut-form ref="formBaseRef" :model-value="state">
        <nut-form-item
          prop="title"
          required
          label="商家名称"
          label-align="left"
          :rules="[
            { required: true, message: '请输入' },
            {
              message: '最多20个字',
              regex: /^.{0,19}$/,
            },
          ]"
        >
          <nut-input
            inputClass="custom-input"
            placeholderClass="custom-input-placeholder"
            placeholder="请输入"
            v-model:model-value="state.title"
          ></nut-input>
        </nut-form-item>
        <nut-form-item
          prop="linkman"
          required
          label="商家联系人"
          label-align="left"
          :rules="[{ required: true, message: '请输入' }]"
        >
          <nut-input
            inputClass="custom-input"
            placeholderClass="custom-input-placeholder"
            placeholder="请输入"
            v-model:model-value="state.linkman"
          ></nut-input>
        </nut-form-item>
        <nut-form-item
          prop="phone"
          required
          label="联系电话"
          label-align="left"
          :rules="[{ required: true, message: '请输入' }]"
        >
          <nut-input
            inputClass="custom-input"
            placeholderClass="custom-input-placeholder"
            placeholder="请输入"
            disabled
            @click="methods.tips('号码不支持修改')"
            v-model:model-value="state.phone"
          ></nut-input>
        </nut-form-item>
        <nut-form-item
          prop="phone"
          required
          label="商家类型"
          label-align="left"
          :rules="[{ required: true, message: '请输入' }]"
        >
          <zk-picker-check
            v-model:value="state.categoryIds"
            :options="storeTypeArray"
            placeholder="请选择"
          ></zk-picker-check>
        </nut-form-item>
        <nut-form-item
          prop="address"
          required
          label="商家地址"
          label-align="left"
          :rules="[{ required: true, message: '请输入' }]"
        >
          <nut-input
            inputClass="custom-input"
            placeholderClass="custom-input-placeholder"
            placeholder="请输入"
            v-model:model-value="state.address"
          ></nut-input>
        </nut-form-item>
      </nut-form>
      <nut-form ref="formImageRef" :model-value="state">
        <nut-form-item
          required
          label-position="top"
          label-align="left"
          prop="photo"
          :rules="[{ required: true, message: '请上传' }]"
        >
          <template #label>
            <zk-text size="32" color="#333">门面照片</zk-text>
          </template>
          <zk-upload-image
            v-model:value="state.photo"
            title="请上传门面照片"
          ></zk-upload-image>
        </nut-form-item>
      </nut-form>
    </view>
    <template #bottom>
      <view class="common-pd">
        <nut-button
          :loading="loading"
          type="primary"
          block
          @click="methods.submit()"
        >
          提交
        </nut-button>
      </view>
    </template>
    <wd-message-box></wd-message-box>
  </ly-default>
</template>

<script setup lang="ts">
import { apiStoreApply, apiStoreListType } from "@/api/store";
import { get, map, size } from "lodash-es";
import { useApiLoading } from "@/hook/useApiLoading";
import { useMessage } from "wot-design-uni";
import { goHome } from "@/router/topage";
import { useUserStore } from "@/store/user";

const message = useMessage();
const userStore = useUserStore();
const { loading, reload } = useApiLoading({
  api: apiStoreApply,
  immediate: false,
  toastLoading: true,
});

interface State {
  phone?: string;
  photo?: string;
  linkman?: string;
  categoryIds?: string;
  address?: string;
  title?: string;
}

const state = reactive<State>({
  phone: undefined,
  photo: undefined,
  linkman: undefined,
  categoryIds: undefined,
  address: undefined,
  title: undefined,
});

const formBaseRef = ref();
const formImageRef = ref();

const storeTypeArray = ref<any[]>([]);

// 获取商家类型
const getStoreList = async () => {
  const res = await apiStoreListType();
  console.log(res);
  if (size(res)) {
    storeTypeArray.value = map(res, (item) => {
      return {
        text: item.name,
        value: item.id,
      };
    });
  }
};

const methods = {
  test() {},
  tips(_tip: string) {
    uni.showToast({
      title: _tip,
      icon: "none",
      duration: 2000,
    });
  },
  async submit() {
    // 开始校验
    const { validate: basicValidate } = await formBaseRef.value!;
    const { validate: imageValidate } = await formImageRef.value!;

    // 同时校验两个
    const [basicV, imageV] = await Promise.all([
      basicValidate(),
      imageValidate(),
    ]);

    if (basicV.valid && imageV.valid) {
      console.log("提交");
      console.log(state);
      reload(state).then(() => {
        userStore.refreshUserInfo();
        message
          .alert({
            title: "提交成功",
            msg: "请耐心等待审核",
          })
          .finally(() => {
            goHome();
          });
      });
    }
  },
  setDefault() {
    state.phone = userStore.phone || undefined;
    if (!userStore.operationAudit) {
      return;
    }
    state.linkman = get(userStore.operationAudit, "linkman");
    state.photo = get(userStore.operationAudit, "photo");
    state.categoryIds = get(userStore.operationAudit, "categoryIds");
    state.address = get(userStore.operationAudit, "address");
    state.title = get(userStore.operationAudit, "title");
  },
};
getStoreList();
methods.setDefault();
</script>

<style scoped lang="scss">
.container {
  // 样式区域
}
</style>
