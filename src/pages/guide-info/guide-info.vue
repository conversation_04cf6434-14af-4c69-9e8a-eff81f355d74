<template>
  <ly-default title="指南详情" title-position="left">
    <view class="common-com common-pd container">
      <view class="container-box">
        <view class="container-title">
          <zk-text size="40" bold :text="title"> </zk-text>
        </view>
        <view class="container-time">
          <zk-text color="#999" :text="updateTime"> </zk-text>
        </view>
        <view class="container-content">
          <rich-text :nodes="nodesData"></rich-text>
        </view>
      </view>
    </view>
  </ly-default>
</template>

<script setup lang="ts">
import { getOpenPolicyGet } from "@/api/open";
import { usePathData } from "@/hook/usePathData";
import { useApiLoading } from "@/hook/useApiLoading";
import { get } from "lodash-es";
const { apiResult, reload } = useApiLoading({
  api: getOpenPolicyGet,
  toastLoading: true,
  immediate: false,
  loadingDefault: true,
});
const { params } = usePathData("", () => {
  // 获取 params type
  if (get(unref(params), "type")) {
    reload(unref(params));
  }
});

const updateTime = computed(() => {
  return get(apiResult.value, "updateTime", "----");
});

const title = computed(() => {
  return get(apiResult.value, "title", "----");
});

const nodesData = computed(() => {
  const text = get(apiResult.value, "content", "");
  if (!text) return "";
  return text.replace(/<(\/)?pre[^>]*>/gi, "");
});
</script>

<style scoped lang="scss">
.container {
  padding-top: 20rpx;
  // 样式区域
  &-box {
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    flex: 1;
  }
  &-title {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20rpx;
    // 文字换行
    word-break: break-all;
    // 内容有换行允许换行
    word-wrap: break-word;
  }
  &-time {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10rpx;
  }
  &-content {
    margin-top: 20rpx;
    overflow: hidden;
    word-break: break-all;
  }
}
</style>

<style>
[alt] {
  max-width: 100%;
  height: auto;
}
</style>
