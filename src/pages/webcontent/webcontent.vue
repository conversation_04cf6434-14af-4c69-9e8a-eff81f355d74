<template>
  <ly-default :title="title" title-position="left">
    <web-view v-if="weburl" :src="weburl"></web-view>
  </ly-default>
</template>

<script setup lang="ts">
import { usePathData } from "@/hook/usePathData";

const { params } = usePathData();
const title = computed(() => unref(params)?.title);
const weburl = computed(() => unref(params)?.url);
</script>

<style scoped lang="scss"></style>
