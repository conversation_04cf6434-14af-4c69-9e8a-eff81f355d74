<template>
  <ly-default title="档案详情" title-position="left">
    <view class="common-com common-pd container">
      <nut-cell-group>
        <nut-cell title="姓名">
          <template #link>
            {{ get(userStore.operationAudit, "name") }}
          </template>
        </nut-cell>
        <nut-cell title="身份证号">
          <template #link>
            {{ get(userStore.operationAudit, "idCard") }}
          </template>
        </nut-cell>
        <nut-cell title="本人电话">
          <template #link>
            {{ get(userStore.operationAudit, "memberPhone") }}
          </template>
        </nut-cell>
        <nut-cell title="学历">
          <template #link>
            {{
              matchMapLabel(
                get(userStore.operationAudit, "education"),
                EducationArray,
              )
            }}
          </template>
        </nut-cell>
      </nut-cell-group>
    </view>
  </ly-default>
</template>

<script setup lang="ts">
import { get } from "lodash-es";
import { useUserStore } from "@/store/user";
import { EducationArray } from "@/maps/op";
import { matchMapLabel } from "../../utils/tools";
const userStore = useUserStore();
</script>

<style scoped lang="scss">
.container {
  // 样式区域
}
</style>
