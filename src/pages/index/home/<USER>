<script setup lang="ts">
import { goToPage } from "@/router/topage";
import { apiMemberGetApplyStage } from "@/api/member";
import { useUserStore } from "@/store/user";
import { useMessage } from "wot-design-uni";
const message = useMessage();
const userStore = useUserStore();
const methods = {
  // 跳转到申请人才学历补贴
  async goToApplyEducationSubsidy() {
    message.alert("功能暂未开放");
    return;
    // 判断是否审批
    if (!userStore.isAuth) {
      message.alert("请先进行个人信息认证");
      return;
    }

    const res = await apiMemberGetApplyStage("education");
    if (res) {
      goToPage("/pages-talent/education-opinion/education-opinion", res);
    } else {
      goToPage("/pages-talent/education-subsidy/education-subsidy");
    }
  },
  // 跳转到申请人才公寓
  goToApplyTalentApartment() {
    message.alert("功能暂未开放");
    return;
    goToPage("/pages-apart/apartment-list/apartment-list");
  },
  // 跳转到就业补贴
  async goToApplyJobSubsidy() {
    message.alert("功能暂未开放");
    return;
    if (!userStore.isAuth) {
      message.alert("请先进行个人信息认证");
      return;
    }
    const res = await apiMemberGetApplyStage("employment");
    if (res) {
      goToPage("/pages-talent/employment-opinion/employment-opinion", res);
    } else {
      goToPage("/pages-talent/employment/employment");
    }
  },
};
</script>

<template>
  <view class="home-top-card">
    <view
      class="home-top-card-item"
      @click="methods.goToApplyEducationSubsidy()"
    >
      <view class="imgbox edu">
        <image class="img" src="/static/Frame1.png"></image>
      </view>
      <zk-text size="28" color="#292B2E">学历补贴</zk-text>
      <zk-text size="20" color="#C9CDD4">待开放</zk-text>
    </view>

    <view class="home-top-card-item" @click="methods.goToApplyJobSubsidy">
      <view class="imgbox job">
        <image class="img" src="/static/ex.png"></image>
      </view>
      <zk-text size="28" color="#292B2E">就业补贴</zk-text>
      <zk-text size="20" color="#C9CDD4">待开放</zk-text>
    </view>
    <view class="home-top-card-item" @click="methods.goToApplyTalentApartment">
      <view class="imgbox apartment">
        <image class="img" src="/static/Frame2.png"></image>
      </view>
      <zk-text size="28" color="#292B2E">人才公寓</zk-text>
      <zk-text size="20" color="#C9CDD4">待开放</zk-text>
    </view>
  </view>
</template>

<style scoped lang="scss">
.home-top-card {
  display: flex;
  width: 100%;
  box-sizing: border-box;
  justify-content: space-around;
  background: #fff;
  padding: 36rpx 24rpx 30rpx 24rpx;
  border-radius: 16rpx;
  &-item {
    display: flex;
    width: 0;
    flex: 1;
    flex-direction: column;
    align-items: center;
    gap: 9rpx;
    & .imgbox {
      width: 90rpx;
      height: 90rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8rpx;
      &.edu {
        background: #fff4ee;
      }
      &.apartment {
        background: #ebf5ff;
      }
      &.job {
        background: #ebfff0;
      }
    }
    & .img {
      width: 57rpx;
      height: 57rpx;
      margin-top: -5rpx;
    }
  }
}
</style>
