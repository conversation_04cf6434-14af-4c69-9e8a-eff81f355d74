<script setup lang="ts">
import { get } from "lodash-es";
import { goToPage } from "@/router/topage";

interface Props {
  item: any;
}
const props = defineProps<Props>();
const methods = {
  goToInfo() {
    goToPage("/pages/consult-info/consult-info", unref(props.item), "event");
  },
};
</script>

<template>
  <view class="home-consult-card-item" @click="methods.goToInfo()">
    <image
      class="home-consult-card-item-img"
      :src="get(item, 'thumbnail')"
    ></image>
    <view class="home-consult-card-item-content">
      <view class="home-consult-card-item-title">
        {{ get(item, "title", "--") }}
      </view>
      <!--      <view class="home-consult-card-item-gl">-->
      <!--        <view class="home-consult-card-item-desc">-->
      <!--          {{ get(item, "detail", "&#45;&#45;") }}-->
      <!--        </view>-->
      <!--      </view>-->

      <view class="home-consult-card-item-time">
        {{ get(item, "updateTime", "--") }}
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.home-consult-card-item {
  display: flex;
  height: 174rpx;
  align-items: center;
  gap: 20rpx;

  &-img {
    width: 240rpx;
    height: 154rpx;
    border-radius: 14rpx;
  }
  &-content {
    height: 150rpx;
    flex-grow: 1;
    width: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-shrink: 0;
  }
  &-gl {
    flex-grow: 1;
    height: 0;
    flex-shrink: 0;
  }
  &-title {
    color: #333333;
    font-size: 30rpx;
    font-weight: 400;
    display: -webkit-box; /* 使用弹性盒子布局 */
    -webkit-box-orient: vertical; /* 设置为垂直方向布局 */
    overflow: hidden; /* 隐藏溢出的内容 */
    text-overflow: ellipsis; /* 溢出时显示省略号 */
    -webkit-line-clamp: 2; /* 限制显示行数为3行 */
  }

  &-desc {
    color: #666;
    font-size: 26rpx;
    font-weight: 400;
    display: -webkit-box; /* 使用弹性盒子布局 */
    -webkit-box-orient: vertical; /* 设置为垂直方向布局 */
    overflow: hidden; /* 隐藏溢出的内容 */
    text-overflow: ellipsis; /* 溢出时显示省略号 */
    -webkit-line-clamp: 2; /* 限制显示行数为3行 */
  }
  &-time {
    color: #999;
    font-size: 24rpx;
    font-weight: 400;
  }
}
</style>
