<script setup lang="ts">
import { goToPage } from "@/router/topage";

interface Props {
  title: string;
  type: string;
}

const props = defineProps<Props>();
const methods = {
  // 查看指南详情
  goGuideDetail() {
    goToPage(
      "/pages/guide-info/guide-info",
      {
        type: props.type,
      },
      "path",
    );
  },
};
</script>

<template>
  <view class="home-guide-card-item">
    <view class="home-guide-card-item-left"> {{ title }} </view>
    <view class="home-guide-card-item-right" @click="methods.goGuideDetail()">
      去查看<nut-icon size="20rpx" name="arrow-right"></nut-icon>
    </view>
  </view>
</template>

<style scoped lang="scss">
.home-guide-card-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100rpx;
  border-radius: 10rpx;
  border: 1rpx solid #eaf5ff;
  background: #f6fbff;
  padding-left: 20rpx;
  padding-right: 24rpx;

  &-left {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &-right {
    color: #999;
    text-align: right;
    font-size: 26rpx;
    font-weight: 400;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
