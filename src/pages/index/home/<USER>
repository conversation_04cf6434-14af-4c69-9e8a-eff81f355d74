<script setup lang="ts">
import { map, size } from "lodash-es";
import { useUserStore } from "@/store/user";

const userStore = useUserStore();

const current = ref<number>(0);

const methods = {
  // 获取轮播图
  getSwiperList: () => {
    const nowTime = Date.now();
    if (
      !size(userStore.swiperList) ||
      nowTime - userStore.swiperTime > 1000 * 60 * 1
    ) {
      userStore.getSwiperList();
    }
  },
};
methods.getSwiperList();
</script>

<template>
  <view class="home-poster-card">
    <wd-swiper
      v-if="size(userStore.swiperList)"
      :list="map(userStore.swiperList, 'url')"
      autoplay
      v-model:current="current"
      :indicator="{ type: 'dots-bar' }"
      height="300rpx"
    ></wd-swiper>
  </view>
</template>

<style scoped lang="scss">
.home-poster-card {
  //background: #fff;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  position: relative;
  min-height: 300rpx;
}
</style>
