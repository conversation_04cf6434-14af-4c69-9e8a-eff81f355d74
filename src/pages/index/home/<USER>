<script setup lang="ts">
import HomeGuideCardItem from "@/pages/index/home/<USER>";
</script>

<template>
  <view class="home-guide-card">
    <view class="home-guide-card-title">
      <view>人才指南</view>
    </view>
    <view class="home-guide-card-content">
      <HomeGuideCardItem
        type="education"
        title="人才学历补贴申请指南"
      ></HomeGuideCardItem>
      <HomeGuideCardItem
        type="apartment"
        title="人才公寓申请指南"
      ></HomeGuideCardItem>
      <HomeGuideCardItem
        type="employment"
        title="就业补贴指南"
      ></HomeGuideCardItem>
    </view>
  </view>
</template>

<style scoped lang="scss">
.home-guide-card {
  //height: 718rpx;
  min-height: 260rpx;
  background: #fff;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  padding: 24rpx 24rpx 37rpx 24rpx;
  &-content {
    margin-top: 30rpx;
    display: flex;
    flex-direction: column;
    gap: 30rpx;
  }
  &-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #333;
    font-size: 30rpx;
    font-weight: 500;
    box-sizing: border-box;
  }
}
</style>
