<script setup lang="ts">
import { apiCouponStatistics } from "@/api/coupon";
import { find, get } from "lodash-es";

const emits = defineEmits<{
  (e: "onTicket", data: any): void;
}>();
const statitics = ref<any>([]);
const methods = {
  // 跳转到 消费券 tab
  goToTicket(code?: string) {
    // 通过 code 获取 categoryId
    const categoryId = get(find(unref(statitics), { code }), "categoryId");
    emits("onTicket", {
      categoryId,
      useState: 1,
    });
  },
  // 获取优惠券数量
  async getCouponCount() {
    statitics.value = await apiCouponStatistics();
  },
  getCountByCode(_statitics: Ref<any[]>, code: string) {
    const item = find(unref(_statitics), { code });
    return item ? item.count : 0; // 如果找不到，返回默认值 0
  },
};

methods.getCouponCount();

const ydu = computed(() => {
  console.log(unref(statitics));
  return methods.getCountByCode(statitics, "YDU");
});
const ys = computed(() => {
  return methods.getCountByCode(statitics, "YS");
});
const yg = computed(() => {
  return methods.getCountByCode(statitics, "YG");
});
const yd = computed(() => {
  return methods.getCountByCode(statitics, "YD");
});
const lwyx = computed(() => {
  return methods.getCountByCode(statitics, "LWYX");
});
</script>

<template>
  <view class="home-ticket-card">
    <view @click="methods.goToTicket('YDU')" class="home-ticket-card-item book">
      <image
        class="img"
        src="https://tzcjlw.oss-cn-shanghai.aliyuncs.com/mini-program/home/<USER>"
      ></image>
      <view class="home-ticket-card-item-content">
        <view>
          <zk-text color="#292B2E" bold size="30rpx">悦读蓝湾</zk-text>
        </view>
        <view>
          <zk-text
            color="#999"
            size="24rpx"
            :text="`${ydu}张券待使用`"
          ></zk-text>
        </view>
      </view>
    </view>
    <view @click="methods.goToTicket('YS')" class="home-ticket-card-item house">
      <image
        class="img"
        src="https://tzcjlw.oss-cn-shanghai.aliyuncs.com/mini-program/home/<USER>"
      ></image>
      <view class="home-ticket-card-item-content">
        <view>
          <zk-text color="#292B2E" bold size="30rpx">悦宿蓝湾</zk-text>
        </view>
        <view>
          <zk-text
            color="#999"
            size="24rpx"
            :text="`${ys}张券待使用`"
          ></zk-text>
        </view>
      </view>
    </view>
    <view @click="methods.goToTicket('YG')" class="home-ticket-card-item mall">
      <image
        class="img"
        src="https://tzcjlw.oss-cn-shanghai.aliyuncs.com/mini-program/home/<USER>"
      ></image>
      <view class="home-ticket-card-item-content">
        <view>
          <zk-text color="#292B2E" bold size="30rpx">悦购蓝湾</zk-text>
        </view>
        <view>
          <zk-text
            color="#999"
            size="24rpx"
            :text="`${yg}张券待使用`"
          ></zk-text>
        </view>
      </view>
    </view>
    <view @click="methods.goToTicket('YD')" class="home-ticket-card-item game">
      <image
        class="img"
        src="https://tzcjlw.oss-cn-shanghai.aliyuncs.com/mini-program/home/<USER>"
      ></image>
      <view class="home-ticket-card-item-content">
        <view>
          <zk-text color="#292B2E" bold size="30rpx">悦动蓝湾</zk-text>
        </view>
        <view>
          <zk-text
            color="#999"
            size="24rpx"
            :text="`${yd}张券待使用`"
          ></zk-text>
        </view>
      </view>
    </view>
    <view
      @click="methods.goToTicket('LWYX')"
      class="home-ticket-card-item house"
    >
      <image
        class="img"
        src="https://tzcjlw.oss-cn-shanghai.aliyuncs.com/mini-program/home/<USER>"
      ></image>
      <view class="home-ticket-card-item-content">
        <view>
          <zk-text color="#292B2E" bold size="30rpx">蓝湾夜校</zk-text>
        </view>
        <view>
          <zk-text
            color="#999"
            size="24rpx"
            :text="`${lwyx}张券待使用`"
          ></zk-text>
        </view>
      </view>
    </view>
    <view @click="methods.goToTicket()" class="home-ticket-card-item mall">
      <image
        class="img imgAll"
        src="https://tzcjlw.oss-cn-shanghai.aliyuncs.com/mini-program/home/<USER>"
      ></image>
      <view class="home-ticket-card-item-content">
        <view>
          <zk-text color="#292B2E" bold size="30rpx">查看更多</zk-text>
        </view>
        <view>
          <zk-text color="#999" size="24rpx" :text="``"></zk-text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.home-ticket-card {
  width: 100%;
  box-sizing: border-box;
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 每行 2 列 */
  gap: 24rpx; /* 盒子之间的间距 */

  &-item {
    height: 154rpx;
    display: flex;
    flex-direction: column;
    gap: 20rpx;
    font-size: 26rpx;
    font-weight: 500;
    transition: all 0.2s;
    position: relative;
    justify-content: center;
    padding: 0 34rpx;

    & .img {
      width: 56rpx;
      height: 56rpx;
      margin-top: -5rpx;
      position: absolute;
      right: 26rpx;
      bottom: 20rpx;
    }

    & .imgAll {
      margin-top: 0rpx;
      top: auto;
      bottom: auto;
    }

    &.book {
      border-radius: 16rpx;
      border: 3rpx solid #fff;
      background: linear-gradient(180deg, #f4e8ff 0%, #fff 100%);
      &:active {
        background: linear-gradient(180deg, #e7d5ff 0%, #fff 100%);
      }
    }

    &.house {
      border-radius: 16rpx;
      border: 3rpx solid #fff;
      background: linear-gradient(180deg, #e8ffef 0%, #fff 100%);
      &:active {
        background: linear-gradient(180deg, #c4fbdc 0%, #fff 100%);
      }
    }

    &.mall {
      border-radius: 16rpx;
      border: 3rpx solid #fff;
      background: linear-gradient(180deg, #e8f4ff 0%, #fff 100%);
      &:active {
        background: linear-gradient(180deg, #c3e1ff 0%, #fff 100%);
      }
    }

    &.game {
      border-radius: 16rpx;
      border: 3rpx solid #fff;
      background: linear-gradient(180deg, #fff2eb 0%, #fff 100%);
      &:active {
        background: linear-gradient(180deg, #fde1d2 0%, #fff 100%);
      }
    }
  }
}
</style>
