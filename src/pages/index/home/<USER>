<script setup lang="ts">
import HomeConsultCardItem from "@/pages/index/home/<USER>";
import { apiGetOpenConsult } from "@/api/open";
import { useApiLoading } from "@/hook/useApiLoading";
import { get } from "lodash-es";
import { goToPage } from "@/router/topage";

const { loading, apiResult } = useApiLoading({
  api: apiGetOpenConsult,
  valueDefault: [],
  params: {
    size: 3,
    current: 1,
  },
});
const methods = {
  goToInfo() {
    goToPage("/pages/consult-list/consult-list");
  },
};
</script>

<template>
  <view class="home-consult-card">
    <view class="home-consult-card-title">
      <view>人才资讯</view>
      <view @click="methods.goToInfo()" class="home-consult-card-title-right">
        查看更多 <nut-icon size="20rpx" name="arrow-right"></nut-icon>
      </view>
    </view>
    <view class="home-consult-card-content">
      <HomeConsultCardItem
        v-for="(item, index) in get(apiResult, 'records', [])"
        :item="item"
        :key="index"
      ></HomeConsultCardItem>
    </view>
    <view class="home-consult-card-loading" v-if="loading">
      <wd-loading size="50rpx" />
    </view>
  </view>
</template>

<style scoped lang="scss">
.home-consult-card {
  //height: 718rpx;
  min-height: 260rpx;
  background: #fff;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  padding: 24rpx 24rpx 37rpx 24rpx;

  &-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100rpx;
  }

  &-content {
    margin-top: 30rpx;
    display: flex;
    flex-direction: column;
    gap: 30rpx;
  }
  &-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #333;
    font-size: 30rpx;
    font-weight: 500;
    box-sizing: border-box;
    &-right {
      color: #999;
      text-align: right;
      font-size: 26rpx;
      font-weight: 400;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
