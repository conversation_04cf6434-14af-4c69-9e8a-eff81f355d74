<script setup lang="ts">
import HoneTitle from "@/pages/index/home/<USER>";
import HomeTopCard from "@/pages/index/home/<USER>";
import HomeGuideCard from "@/pages/index/home/<USER>";
import HomeConsultCard from "@/pages/index/home/<USER>";
import HomePosterCard from "@/pages/index/home/<USER>";
import HomeTicketCard from "@/pages/index/home/<USER>";
import { useLoginStore } from "@/store/login";

const emits = defineEmits(["onActiveName"]);
const loginStore = useLoginStore();
const methods = {
  // 跳转到 消费券 tab
  goToTicket(options: any) {
    emits("onActiveName", "Ticket", options);
  },
};
</script>

<template>
  <view class="index-home">
    <view class="index-home-bg"> </view>
    <!--    <image-->
    <!--      class="index-home-topi"-->
    <!--      src="https://tzcjlw.oss-cn-shanghai.aliyuncs.com/mini-program/image2337.png"-->
    <!--    ></image>-->
    <!--    <view class="index-home-topib"></view>-->
    <!--    <view class="index-home-topt">-->
    <!--      <HoneTitle></HoneTitle>-->
    <!--    </view>-->
    <view class="index-home-box">
      <HomePosterCard></HomePosterCard>
      <HomeTicketCard
        v-if="loginStore.isLogin"
        @on-ticket="methods.goToTicket"
      ></HomeTicketCard>
      <!-- <HomeTopCard></HomeTopCard> -->
      <HomeConsultCard></HomeConsultCard>
      <HomeGuideCard v-if="false"></HomeGuideCard>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.index-home {
  box-sizing: border-box;
  position: relative;
  display: flex;
  flex-direction: column;

  &-box {
    padding: 0 24rpx;
    margin-top: 20rpx;
    z-index: 1;
    display: flex;
    flex-direction: column;
    gap: 40rpx;
    box-sizing: border-box;
  }

  &-topt {
    position: absolute;
    top: 50rpx;
    left: 24rpx;
  }

  &-topib {
    width: 263rpx;
    height: 263rpx;
    position: absolute;
    top: 40rpx;
    right: 35rpx;
    border-radius: 263rpx;
    border: 2px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    filter: blur(1rpx);
    z-index: -1;
  }

  &-topi {
    width: 183rpx;
    height: 210rpx;
    position: absolute;
    top: 40rpx;
    right: 80rpx;
    &:after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
    }
  }

  &:before {
    content: "";
    position: fixed;
    top: 0;
    width: 100%;
    height: 596rpx;
    background: linear-gradient(
      180deg,
      #bcdbfb 26.93%,
      rgba(203, 228, 254, 0.01) 100%
    );
    border-radius: 0 0 5% 5%;
    z-index: -1;
  }

  &-bg {
    position: fixed;
    top: -62rpx;
    left: -98rpx;
    width: 257rpx;
    height: 257rpx;
    border-radius: 257rpx;
    border: 2rpx solid rgba(255, 255, 255, 0.6);
    opacity: 0.5;
    background: linear-gradient(
      180deg,
      #b1e7f9 0%,
      rgba(255, 255, 255, 0) 100%
    );

    &::before {
      content: "";
      position: absolute;
      top: -25rpx;
      left: 111rpx;
      width: 257rpx;
      height: 257rpx;
      border-radius: 257rpx;
      background: #b4e5f9;
      filter: blur(22rpx);
    }
  }
}
</style>
