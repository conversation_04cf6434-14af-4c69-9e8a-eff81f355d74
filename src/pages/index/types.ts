export type ComponentName =
  | "Home"
  | "Ticket"
  | "User"
  | "NotLogin"
  | "Error"
  | "StoreHome"
  | "NotInfo"
  | "NotAudit"
  | "WaitAudit"
  | "StoreTicket";

export type TabbarKey = "Home" | "Ticket" | "User";
export interface TabbarItem {
  key: TabbarKey;
  iconPath: string;
  iconPathActive: string;
  isLogin: boolean;
  component: {
    isInfo: boolean;
    clientId: string;
    name: ComponentName;
    title: string;
    nav: string;
    navBg: boolean;
  }[];
}

export type TabbarListType = TabbarItem[];

export interface RenderTabbarItem {
  key: TabbarKey;
  iconPath: string;
  iconPathActive: string;
  isLogin: boolean;
  clientId: string;
  name: ComponentName;
  title: string;
  nav: string;
  navBg: boolean;
  isInfo: boolean;
}
export type RenderTabbarItemType = RenderTabbarItem[];
