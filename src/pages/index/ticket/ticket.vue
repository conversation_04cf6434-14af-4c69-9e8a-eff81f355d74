<script lang="ts" setup>
	import TicketCard from './ticket-card.vue'
	import TicketCardActivity from './ticket-card-activity.vue'
	import {
		apiCouponCategoryList,
		apiCouponPage,
		apiCouponUserWaitList,
		apiMemberOneClickCollection,
	} from '@/api/coupon'
	import { apiMemberReceive } from '@/api/member'
	import { useScrollView } from '@/hook/useScrollView'
	import { get, map, size } from 'lodash-es'
	import { useMessage } from 'wot-design-uni'

	const props = defineProps<{
		cacheTicketCategoryId?: string | null
		cacheUseState?: number | null
	}>()

	const emits = defineEmits(['update:cacheTicketCategoryId'])

	const message = useMessage()
	// "AWAIT",
	const useStateMap = ['AWAIT', 'INIT', 'OVERDUE']

	const category = ref(props.cacheTicketCategoryId)

	const useState = ref(props.cacheUseState || 0)

	const useStateType = computed(() => {
		if (unref(useState) >= 0) {
			return useStateMap[unref(useState)]
		}
		return useStateMap[0]
	})

	// 计算属性查询参数
	const params = computed(() => {
		return {
			categoryId: unref(category),
			useState: unref(useStateType),
		}
	})

	const {
		loading,
		refresherStatus,
		onPullDownRefresh,
		onReachBottom,
		list,
		clearList,
		isEmpty,
		reload,
		extra,
		total,
	} = useScrollView<any>({
		api: async (params) => {
			if (params?.useState === 'AWAIT') {
				const res = await apiCouponUserWaitList()
				const records = get(res, 'operationCoupons', []) || []
				return {
					records: records,
					pages: 1,
					total: size(records),
					current: 1,
					size: 1,
					info: res,
				}
			}
			return apiCouponPage(params)
		},
		params,
	})

	const categoryList = ref([
		{
			label: '全部',
			value: null,
		},
	])

	// 领取按钮文案
	const receiveText = computed(() => {
		const type = get(unref(extra), 'info.type')
		if (type === 'WAIT_RECEIVE') {
			return '一键领取'
		} else if (type === 'RECEIVED') {
			return '你已领取本季度消费券'
		} else if (type === 'SOLD_OUT') {
			return '本季度消费券已全部被领取'
		}
		return '无法操作'
	})

	// 是否拥有活动
	const hasActivity = computed(() => {
		return (
			!!get(unref(extra), 'info.operationActivity') &&
			get(unref(extra), 'info.type') !== 'SOLD_OUT'
		)
	})

	// 空列表文案
	const emptyText = computed(() => {
		if (unref(useStateType) === 'AWAIT' && !unref(hasActivity)) {
			return '本季度消费券已全部被领取，敬请关注下期活动'
		}
		return '暂无可用优惠券'
	})

	// 按钮禁用状态
	const disabledButton = computed(() => {
		const type = get(unref(extra), 'info.type')
		return !unref(hasActivity) || unref(total) === 0 || type !== 'WAIT_RECEIVE'
	})

	// 按钮类型切换
	const buttonType = computed(() => {
		return unref(disabledButton) ? 'info' : 'primary'
	})

	const methods = {
		// 获取分类
		async getCategory() {
			categoryList.value = map((await apiCouponCategoryList()) || [], (item) => {
				return {
					label: item.name,
					value: item.id,
				}
			})
		},
		// 回调
		onGetCoupon(v: any) {
			message
				.confirm({
					title: '提示',
					msg: '确定领取该优惠券吗？',
				})
				.then(() => {
					apiMemberReceive(v)
						.then((res) => {
							console.log(res)
						})
						.finally(() => {
							reload()
						})
				})
				.catch((e) => {
					console.log('用户点击取消')
				})
		},
		// 一键领取
		async onOneClickCollection() {
			// 从扩展信息中获取 activityId
			const activityId = get(unref(extra), 'info.operationActivity.id')
			if (!activityId) {
				uni.showToast({
					title: '活动不存在',
					icon: 'none',
				})
				return
			}

			message
				.confirm({
					title: '提示',
					msg: '确定领取优惠券吗？',
				})
				.then(async () => {
					await apiMemberOneClickCollection(activityId)
					message.show('领取成功')
				})
				.finally(() => {
					reload()
				})
		},
	}
	methods.getCategory()

	onMounted(() => {
		watch(params, (v) => {
			clearList()
			reload()
		})
		if (props.cacheTicketCategoryId) {
			emits('update:cacheTicketCategoryId', null)
		}
	})
</script>

<template>
	<view class="home-ticket">
		<wd-tabs v-model="useState" autoLineWidth>
			<wd-tab title="待领取"> </wd-tab>
			<wd-tab title="待使用"> </wd-tab>
			<wd-tab title="已过期"> </wd-tab>
		</wd-tabs>
		<zk-category
			v-if="useStateType !== 'AWAIT'"
			v-model:value="category"
			:options="categoryList"
		></zk-category>

		<scroll-view
			:scroll-y="true"
			class="common-scroll home-ticket-scroll"
			:refresher-enabled="true"
			@refresherrefresh="onPullDownRefresh"
			:refresher-triggered="refresherStatus"
			@scrolltolower="onReachBottom"
		>
			<view class="common-list" v-if="useStateType === 'AWAIT'">
				<view class="home-ticket-tips" v-if="hasActivity">
					<wd-text text="本季度券包内含"></wd-text>
					<wd-text type="error" :text="`共${total}张`"></wd-text>
					<wd-text text="消费券；每位符合条件用户"></wd-text>
					<wd-text
						type="error"
						:text="`限领${get(extra, 'info.operationActivity.perCapita', 0) || 0}次`"
					></wd-text>
					<wd-text text="，点击下方按钮即可一键领取全部优惠券。当前活动共支持"></wd-text>
					<wd-text
						type="error"
						:text="`${get(extra, 'info.operationActivity.peopleCount', 0) || 0}人领取`"
					></wd-text>
					<wd-text text="，先到先得，用完即止。"></wd-text>
				</view>
				<TicketCardActivity
					:useState="useState"
					v-for="item in list"
					:disabled="disabledButton"
					:item="item"
					:key="item.id"
				/>
			</view>
			<view class="common-list" v-else>
				<TicketCard
					:useState="useState"
					v-for="item in list"
					:item="item"
					:key="item.id"
					@onGetCoupon="methods.onGetCoupon(item)"
				/>
			</view>

			<view class="common-empty" v-if="isEmpty">
				<wd-status-tip image="content" :tip="emptyText" />
			</view>
			<zk-loading :loading="loading"></zk-loading>
		</scroll-view>
		<view class="home-ticket-bottom" v-if="useStateType === 'AWAIT' && hasActivity">
			<wd-button
				:type="buttonType"
				@click="methods.onOneClickCollection"
				:disabled="disabledButton"
				block
			>
				{{ receiveText }}
			</wd-button>
		</view>
	</view>
</template>

<style scoped lang="scss">
	.home-ticket {
		display: flex;
		flex-direction: column;
		box-sizing: border-box;
		height: 100%;
		position: relative;
		&-tips {
			padding: 0 10rpx 0 14rpx;
			box-sizing: border-box;
			font-size: 28rpx;
		}
		&-bottom {
			width: 100%;
			padding: 40rpx 24rpx 24rpx 24rpx;
			box-sizing: border-box;
			background-color: white;
		}
	}
</style>

<style lang="scss">
	.nut-tab-pane {
		display: none;
	}
</style>
