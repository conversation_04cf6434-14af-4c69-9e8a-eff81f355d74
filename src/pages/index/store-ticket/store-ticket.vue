<script setup lang="ts">
import StoreHomeItem from "@/pages/index/store-home/store-home-item.vue";
import { useScrollView } from "@/hook/useScrollView";
import { apiStoreRecord } from "@/api/store";
import { get } from "lodash-es";
import numbro from "numbro";
const settleType = ref(false);
const params = computed(() => {
  return {
    closing: settleType.value,
    periodEnum: "ALL",
  };
});

const {
  status,
  refresherStatus,
  onPullDownRefresh,
  onReachBottom,
  list,
  clearList,
  extra,
  loading,
  reload,
  isEmpty,
} = useScrollView({
  api: apiStoreRecord,
  params,
});
const options = computed(() => {
  const not_settle = numbro(get(unref(extra), "not_settle", 0)).format({
    // 千分位
    thousandSeparated: true,
    // 小数点后位数
    mantissa: 2,
    // 货币符号
    prefix: "¥",
  });
  const settle = numbro(get(unref(extra), "settle", 0)).format({
    // 千分位
    thousandSeparated: true,
    // 小数点后位数
    mantissa: 2,
    // 货币符号
    prefix: "¥",
  });

  return [
    {
      label: "待核算",
      value: false,
      title: not_settle,
    },
    {
      label: "已结算",
      value: true,
      title: settle,
    },
  ];
});
onMounted(() => {
  watch(params, (v) => {
    clearList();
    reload();
  });
});
</script>

<template>
  <view class="store-ticket common-bg3">
    <view class="store-ticket-card">
      <zk-radio-four
        v-model:value="settleType"
        :options="options"
      ></zk-radio-four>
      <view class="store-ticket-select" v-if="false">
        <view> 全部 </view>
        <view class="icon">
          <nut-icon size="24rpx" name="triangle-down"></nut-icon>
        </view>
      </view>
      <scroll-view
        :scroll-y="true"
        class="store-ticket-scroll"
        :refresher-enabled="true"
        @refresherrefresh="onPullDownRefresh"
        :refresher-triggered="refresherStatus"
        @scrolltolower="onReachBottom"
      >
        <view class="store-ticket-list">
          <store-home-item
            v-for="(item, index) in list"
            :item="item"
            :key="index"
          ></store-home-item>
        </view>
        <view class="store-ticket-empty" v-if="isEmpty">
          <wd-status-tip image="content" tip="暂无内容" />
        </view>
        <zk-loading :loading="loading"></zk-loading>
      </scroll-view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.store-ticket {
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  &-select {
    display: flex;
    align-items: center;
    font-size: 32rpx;
    color: #333;
    margin-top: 56rpx;
    font-weight: 500;
    .icon {
      margin-top: 12rpx;
    }
  }

  &-card {
    flex: 1;
    border-radius: 16rpx 16rpx 0 0;
    background: #fff;
    margin: 40rpx 24rpx 0 24rpx;
    padding: 40rpx 24rpx 0 24rpx;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    flex-shrink: 0;
    height: 0;
  }
  &-list {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    padding: 24rpx;
    box-sizing: border-box;
  }
  &-scroll {
    flex-grow: 1;
    overflow: auto;
    flex-shrink: 0;
    height: 0;
    box-sizing: border-box;
    margin: 24rpx -24rpx 0 -24rpx;
    width: auto;
    position: relative;
  }
  &-empty {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin-top: -50rpx;
  }
}
</style>
