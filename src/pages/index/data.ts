import type { TabbarListType } from "@/pages/index/types";

export const tabbarList: TabbarListType = [
  {
    key: "Home",
    iconPath: "/static/icon/tab1.png",
    iconPathActive: "/static/icon/tab1a.png",
    // 是否需要登录
    isLogin: false,
    // 不同用户不同的组件
    component: [
      {
        clientId: "mini",
        name: "Home",
        title: "首页",
        nav: "才聚蓝湾 悦享生活",
        navBg: true,
        isInfo: false,
      },
      {
        clientId: "store",
        name: "StoreHome",
        title: "首页",
        nav: "",
        navBg: true,
        isInfo: false,
      },
    ],
  },
  {
    key: "Ticket",
    iconPath: "/static/icon/tab2.png",
    iconPathActive: "/static/icon/tab2a.png",
    isLogin: true,

    component: [
      {
        clientId: "mini",
        name: "Ticket",
        title: "消费券",
        nav: "消费券",
        isInfo: true,
        navBg: false,
      },
      {
        clientId: "store",
        name: "StoreTicket",
        title: "核销明细",
        nav: "核销明细",
        navBg: true,
        isInfo: false,
      },
    ],
  },
  {
    key: "User",
    iconPath: "/static/icon/tab3.png",
    iconPathActive: "/static/icon/tab3a.png",
    isLogin: false,
    component: [
      {
        clientId: "mini",
        name: "User",
        title: "我的",
        nav: "",
        navBg: true,
        isInfo: false,
      },
      {
        clientId: "store",
        name: "User",
        title: "我的",
        nav: "",
        navBg: true,
        isInfo: false,
      },
    ],
  },
];
