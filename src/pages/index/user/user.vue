<script setup lang="ts">
import { useLoginStore } from "@/store/login";
import { useUserStore } from "@/store/user";
import { useMessage } from "wot-design-uni";
import {
  goEditUser,
  goLogin,
  goSubscribeRecord,
  goToDebug,
  goToPage,
} from "@/router/topage";
import { get } from "lodash-es";
const userStore = useUserStore();
const loginStore = useLoginStore();
const message = useMessage();
const methods = {
  // 跳转到登录页面
  toLogin() {
    uni.navigateTo({
      url: "/pages/login/login",
    });
  },
  // 退出登录
  logout() {
    console.log("退出登录");
    message
      .confirm({
        title: "提示",
        msg: "确定退出登录吗？",
      })
      .then(() => {
        console.log("用户点击确定");
        loginStore.logout();
        uni.showToast({
          title: "退出登录成功",
          icon: "success",
        });
      })
      .catch((e) => {
        console.log("用户点击取消");
      });
  },
  openSetting() {
    uni.openSetting();
  },
  goToAbout() {
    uni.navigateTo({
      url: "/pages/about/about",
    });
  },
  goOrderList() {
    goSubscribeRecord();
  },
  goToTicket() {
    uni.navigateTo({
      url: "/pages/ticket-record/ticket-record",
      success: (res) => {
        console.log(res);
      },
    });
  },
  onclickInfo() {
    // 判断是否已经登录
    if (loginStore.isLogin) {
      goEditUser();
    } else {
      goLogin();
    }
  },
  call() {
    const phone = "051381680227";
    uni
      .makePhoneCall({
        phoneNumber: phone,
      })
      .catch((err) => {
        if (get(err, "errMsg") === "makePhoneCall:fail cancel") {
          message.alert({
            title: "拨打已取消",
            msg: `号码：${phone}`,
          });
          return;
        }
        message.alert({
          title: "拨号失败",
          msg: `号码：${phone}`,
        });
      });
  },
};
</script>

<template>
  <view class="index-user common-bg">
    <view class="index-user-base">
      <image
        class="index-user-base-image"
        :src="userStore.avatarUrl"
        mode="aspectFill"
        @click="methods.onclickInfo"
      />
      <view
        class="index-user-info"
        v-if="loginStore.isLogin"
        @click="methods.onclickInfo()"
      >
        <view> {{ userStore.maskUserName }} </view>
      </view>
      <view class="index-user-login" @click="methods.onclickInfo()" v-else>
        登录/注册
      </view>
    </view>
    <view class="index-user-cell">
      <nut-cell-group>
        <nut-cell is-link icon="edit" @click="goEditUser()" title="修改资料">
        </nut-cell>
        <nut-cell
          v-if="!userStore.isStore"
          @click="goToPage('/pages-talent/record/record')"
          is-link
          title="学历补贴填报记录"
        >
          <template #icon>
            <image
              class="index-user-cell-icon"
              src="/static/icon/list1.png"
            ></image>
          </template>
        </nut-cell>
        <nut-cell
          v-if="!userStore.isStore"
          @click="goToPage('/pages-talent/employment-record/employment-record')"
          is-link
          title="就业补贴填报记录"
        >
          <template #icon>
            <image
              class="index-user-cell-icon"
              src="/static/icon/list1.png"
            ></image>
          </template>
        </nut-cell>
        <nut-cell
          v-if="!userStore.isStore"
          is-link
          title="人才公寓填报记录"
          @click="goToPage('/pages-apart/record/record')"
        >
          <template #icon>
            <image
              class="index-user-cell-icon"
              src="/static/icon/list2.png"
            ></image>
          </template>
        </nut-cell>
        <nut-cell
          v-if="!userStore.isStore"
          is-link
          title="消费券使用记录"
          @click="methods.goToTicket()"
        >
          <template #icon>
            <image
              class="index-user-cell-icon"
              src="/static/icon/list3.png"
            ></image>
          </template>
        </nut-cell>
        <nut-cell
          icon="setting"
          is-link
          title="授权管理"
          @click="methods.openSetting()"
        >
        </nut-cell>
        <nut-cell
          icon="service"
          is-link
          title="联系我们"
          @click="methods.call()"
        ></nut-cell>
        <!--        <nut-cell-->
        <!--          is-link-->
        <!--          title="调试"-->
        <!--          icon="ask"-->
        <!--          @click="goToDebug()"-->
        <!--        ></nut-cell>-->
        <!--        <nut-cell-->
        <!--          v-if="loginStore.isLogin"-->
        <!--          is-link-->
        <!--          title="退出登录"-->
        <!--          icon="footprint"-->
        <!--          @click="methods.logout()"-->
        <!--        ></nut-cell>-->
      </nut-cell-group>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.index-user {
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  &-base {
    margin-top: 40rpx;
    height: 140rpx;
    display: flex;
    align-items: center;
    padding: 0 50rpx;
    gap: 24rpx;
    &-image {
      width: 140rpx;
      height: 140rpx;
      border-radius: 50%;
      border: #eee 1rpx solid;
    }
  }
  &-info {
    display: flex;
    flex-direction: column;
    font-size: 36rpx;
    gap: 10rpx;
  }
  &-login {
    display: flex;
    gap: 10rpx;
    font-size: 38rpx;
    align-items: center;
    color: #7192e5;
    position: relative;
    // 右边箭头
    &::after {
      content: "";
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%) translateX(30rpx) rotate(45deg);
      width: 18rpx;
      height: 18rpx;
      border-top: 2rpx solid #7192e5;
      border-right: 2rpx solid #7192e5;
    }
  }
  &-sex {
    padding: 2rpx 10rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;

    &.male {
      background: #7192e5;
    }
    &.female {
      background: #ff7192e5;
    }
  }

  &-cell {
    margin-top: 40rpx;
    padding: 0 24rpx;
    box-sizing: border-box;

    &-icon {
      width: 36rpx;
      height: 36rpx;
    }
  }
}
</style>
