<script setup lang="ts">
import { get } from "lodash-es";

interface Props {
  item?: any;
}
defineProps<Props>();
</script>

<template>
  <view class="store-home-item">
    <view class="store-home-item-left">
      <zk-text size="26">
        <text> 券码 {{ get(item, "id") }} </text>
      </zk-text>
      <zk-text size="24" color="#999">
        <text> {{ get(item, "useTime") }} </text>
      </zk-text>
    </view>
    <zk-text color="#1677FF" size="36">
      <text> {{ get(item, "price") }} </text>
    </zk-text>
  </view>
</template>

<style scoped lang="scss">
.store-home-item {
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  height: 120rpx;
  align-items: center;
  border-bottom: 1rpx rgba(0, 0, 0, 0.06) solid;
  margin: 0 24rpx;
  &-left {
    display: flex;
    flex-direction: column;
    gap: 10rpx;
  }
}
</style>
