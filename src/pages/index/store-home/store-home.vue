<script setup lang="ts">
import StoreHomeTitle from "@/pages/index/store-home/store-home-title.vue";
import StoreHomeItem from "@/pages/index/store-home/store-home-item.vue";
import querystring from "query-string";
import { get, size } from "lodash-es";
import { useMessage } from "wot-design-uni";
import { apiStoreVerification, apiStoreRecord } from "@/api/store";
import { goTicketResult, goToPage } from "@/router/topage";
import { useScrollView } from "@/hook/useScrollView";
import { useUserStore } from "@/store/user";
import { onShow } from "@dcloudio/uni-app";

const message = useMessage();
const userStore = useUserStore();
const emit = defineEmits(["onToTicket"]);

const {
  refresherStatus,
  onPullDownRefresh,
  onReachBottom,
  list,
  clearList,
  loading,
  reload,
} = useScrollView({
  api: apiStoreRecord,
  params: {
    periodEnum: "RECENTLY_MONTH",
  },
});

const methods = {
  toTicket() {
    emit("onToTicket");
  },
  openScanCode() {
    uni.scanCode({
      scanType: ["qrCode"],
      success: async (res) => {
        try {
          const result = get(res, "result");
          if (result) {
            // 根据扫码内容跳转到下单页面
            const params = querystring.parse(result);
            if (!get(params, "id")) {
              return message.alert("无效的券码");
            }
            await apiStoreVerification(params);
            goTicketResult({
              status: "success",
              code: params.id,
            });
            reload();
          }
        } catch (e) {
          console.log("错误拦截", e);
        }
      },
      fail: (error) => {
        console.log(error);
      },
    });
  },
  // 入驻申请
  toApply() {
    goToPage("/pages/store-enter/store-enter");
  },
  // 刷新用户信息
  async refreshUserInfo() {
    // 获取审核状态
    const auditStatus = userStore.audit;
    if (auditStatus !== "approve") {
      userStore.refreshUserInfo();
    }
  },
};

methods.refreshUserInfo();
</script>

<template>
  <view class="store-home common-bg1">
    <StoreHomeTitle></StoreHomeTitle>
    <view class="store-home-card" v-if="userStore.audit === 'approve'">
      <nut-button
        @click="methods.openScanCode"
        block
        shape="square"
        type="primary"
        size="large"
      >
        <template #icon>
          <nut-icon name="scan2" />
        </template>
        扫描消费券二维码
      </nut-button>
      <view class="store-home-tips">
        <zk-text size="32">最近一个月核销的券</zk-text>
        <zk-text color="#999" @click="methods.toTicket" size="26">
          查看更多>
        </zk-text>
      </view>

      <scroll-view
        :scroll-y="true"
        class="store-home-scroll"
        :refresher-enabled="true"
        @refresherrefresh="onPullDownRefresh"
        :refresher-triggered="refresherStatus"
        @scrolltolower="onReachBottom"
      >
        <view class="store-home-list">
          <store-home-item
            v-for="(item, index) in list"
            :item="item"
            :key="index"
          ></store-home-item>
          <view class="store-home-empty" v-if="size(list) === 0">
            <wd-status-tip image="content" tip="暂无内容" />
          </view>
        </view>
        <zk-loading top="150" :loading="loading"></zk-loading>
      </scroll-view>
    </view>

    <view class="store-home-card" v-else-if="userStore.audit === 'stage'">
      <view class="store-home-stage">
        <zk-text color="#86909C" size="32" align="center">
          您尚未完成商家入驻，请先提交商家资料申请，审核通过后即可成功入驻。
        </zk-text>
        <nut-button type="primary" @click="methods.toApply()">
          申请入驻
        </nut-button>
      </view>
    </view>
    <view class="store-home-card" v-else-if="userStore.audit === 'init'">
      <view class="store-home-stage">
        <zk-text color="#86909C" size="32" align="center">
          您的商家入驻信息已提交，正在审核中，请耐心等待审核结果。
        </zk-text>
      </view>
    </view>
    <view class="store-home-card" v-else-if="userStore.audit === 'refuse'">
      <view class="store-home-stage">
        <zk-text
          color="#86909C"
          size="32"
          align="center"
          :text="` 您的商家入驻申请未通过审核，原因是:【${userStore.storeReasons}】`"
        >
        </zk-text>
        <nut-button type="primary" @click="methods.toApply()">
          再次申请入驻
        </nut-button>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.store-home {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  height: 100%;
  &-stage {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 386rpx;
    gap: 50rpx;
  }
  &-tips {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 60rpx;
    margin-bottom: 20rpx;
  }
  &-card {
    margin: 94rpx 24rpx 0 24rpx;
    flex-grow: 1;
    flex-shrink: 0;
    height: 0;
    box-sizing: border-box;
    background: #fff;
    border-radius: 16rpx 16rpx 0 0;
    padding: 80rpx 24rpx 0 24rpx;
    display: flex;
    flex-direction: column;
  }
  &-list {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    padding: 24rpx;
    box-sizing: border-box;
  }
  &-scroll {
    flex-grow: 1;
    overflow: auto;
    flex-shrink: 0;
    height: 0;
    box-sizing: border-box;
    margin: 0 -24rpx;
    width: auto;
    position: relative;
  }
  &-empty {
    margin-top: 200rpx;
  }
}
</style>
