<script setup lang="ts">
import type {
  ComponentName,
  RenderTabbarItem,
  RenderTabbarItemType,
} from './types'
import NotAudit from '@/layout/NotAudit.vue'
import NotInfo from '@/layout/NotInfo.vue'
import NotLogin from '@/layout/NotLogin.vue'
import WaitAudit from '@/layout/WaitAudit.vue'
import { useLoginStore } from '@/store/login'
import { useUserStore } from '@/store/user'
import { decodeObjectRecursive } from '@/utils/querytool'
import { onLoad, onShareAppMessage } from '@dcloudio/uni-app'
import { find, get, map } from 'lodash-es'
import { ref } from 'vue'
import { useMessage } from 'wot-design-uni'
import { tabbarList } from './data'
import Home from './home/<USER>'
import StoreHome from './store-home/store-home.vue'
import StoreTicket from './store-ticket/store-ticket.vue'
import Ticket from './ticket/ticket.vue'

import User from './user/user.vue'

onLoad((option) => {
  const { name } = decodeObjectRecursive(option)
  if (name) {
    activeName.value = name
  }
})
const message = useMessage()
const loginStore = useLoginStore()
const userStore = useUserStore()
const clientId = ref('store')
const cacheTicketCategoryId = ref<null | string>(null)
const cacheUseState = ref<null | number>(null)
const renderTabbar = computed<RenderTabbarItemType>(() => {
  return map(tabbarList, (item) => {
    const cur = find(item.component, { clientId: loginStore.clientId })
    return {
      ...item,
      ...(cur || get(item, 'component.0')),
    }
  })
})

onShareAppMessage(() => {
  return {
    title: '才聚蓝湾 悦享生活',
    path: '/pages/index/index',
  }
})

const activeName = ref<ComponentName>('Home')

const showComponentName = computed<ComponentName>(() => {
  const item = find(renderTabbar.value, {
    key: activeName.value,
  }) as RenderTabbarItem | undefined
  if (!item)
    return 'Error'
  if (!loginStore.isLogin && item.isLogin) {
    return 'NotLogin'
  }
  if (item.isInfo && userStore.auth !== 'success') {
    userStore.refreshUserInfo()
    switch (userStore.auth) {
      case 'init':
        return 'NotInfo'
      case 'wait':
        return 'WaitAudit'
      case 'fail':
        return 'NotAudit'
      default:
        return 'NotInfo'
    }
  }

  return item.name
})

const navbarStyle = computed(() => {
  const item = find(renderTabbar.value, {
    key: activeName.value,
  }) as RenderTabbarItem | undefined
  if (!item)
    return 'background-color: transparent !important'
  if (!loginStore.isLogin && item.isLogin) {
    return 'background-color: transparent !important'
  }
  if (item.navBg) {
    return 'background-color: transparent !important'
  }
  return ''
})

const navbarTitle = computed(() => {
  const item = find(renderTabbar.value, {
    key: activeName.value,
  }) as RenderTabbarItem | undefined
  if (!item)
    return ''
  if (!loginStore.isLogin && item.isLogin) {
    return ''
  }
  return item.nav
})

const methods = {
  toTicket() {
    activeName.value = 'Ticket'
  },
  test() {
    if (clientId.value === 'mini') {
      clientId.value = 'store'
    }
    else {
      clientId.value = 'mini'
    }
  },
  scanCode() {
    uni.scanCode({
      scanType: ['qrCode'],
      success: (res) => {
        console.log(res)
        const result = get(res, 'result')
        if (result) {
          // 根据扫码内容跳转到下单页面
          message.show({
            title: '扫码内容',
            msg: result,
          })
        }
      },
      fail: (error) => {
        console.log(error)
      },
    })
  },
  // 激活 tab
  onActiveName(name: ComponentName, options?: any) {
    const { categoryId, useState } = options || {}
    if (name === 'Ticket' && categoryId) {
      // 设置 ticket 的 categoryId
      cacheTicketCategoryId.value = categoryId
      cacheUseState.value = useState || 0
    }
    activeName.value = name
  },
}
</script>

<template>
  <view class="container">
    <view class="container-navbar">
      <wd-navbar
        :custom-style="navbarStyle"
        placeholder
        safe-area-inset-top
        :bordered="false"
      >
        <template #title>
          <view class="container-navbar-title">
            {{ navbarTitle }}
          </view>
        </template>
        <template #left>
          <view
            v-if="false"
            class="container-navbar-scan"
            @click="methods.test()"
          >
            <wd-icon name="scan" size="22px" />
          </view>
        </template>
      </wd-navbar>
    </view>
    <view class="container-box">
      <Home
        v-if="showComponentName === 'Home'"
        class="container-home"
        @on-active-name="methods.onActiveName"
      />
      <StoreHome
        v-else-if="showComponentName === 'StoreHome'"
        class="container-home"
        @on-to-ticket="methods.toTicket"
        @on-scan-code="methods.scanCode"
      />
      <Ticket
        v-else-if="showComponentName === 'Ticket'"
        v-model:cache-ticket-category-id="cacheTicketCategoryId"
        v-model:cache-use-state="cacheUseState"
        class="container-home"
      />
      <StoreTicket
        v-else-if="showComponentName === 'StoreTicket'"
        class="container-home"
      />
      <User
        v-else-if="showComponentName === 'User'"
        class="container-home"
      />
      <NotLogin
        v-else-if="showComponentName === 'NotLogin'"
        class="container-home"
      />
      <NotInfo
        v-else-if="showComponentName === 'NotInfo'"
        class="container-home"
      />
      <NotAudit
        v-else-if="showComponentName === 'NotAudit'"
        class="container-home"
      />
      <WaitAudit
        v-else-if="showComponentName === 'WaitAudit'"
        class="container-home"
      />
    </view>
    <view>
      <nut-tabbar
        v-model="activeName"
        bottom
        safe-area-inset-bottom
        placeholder
      >
        <nut-tabbar-item
          v-for="item in renderTabbar"
          :key="item.key"
          :name="item.key"
          :tab-title="item.title"
        >
          <template #icon="props">
            <view>
              <img v-show="props.active" :src="item.iconPathActive">
              <img v-show="!props.active" :src="item.iconPath">
            </view>
          </template>
        </nut-tabbar-item>
      </nut-tabbar>
    </view>

    <wd-message-box />
    <zk-env-watermark />
  </view>
</template>

<style lang="scss" scoped>
.container {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  &-navbar {
    flex-shrink: 0;
    &-title {
      color: #333;
      font-size: 34rpx;
      font-weight: 400;
      left: 50rpx;
      position: absolute;
      z-index: 10;
    }
    &-scan {
      color: #333;
      transition: all 0.2s;
      z-index: 1;

      &:active {
        color: #fff;
      }
      &.icon {
        color: #fff;
        &:active {
          color: #333;
        }
      }
    }
  }
  &-box {
    height: 0;
    flex-grow: 1;
    overflow: auto;
    box-sizing: border-box;
  }
  &-home {
    height: 100%;
    position: relative;
  }
}
</style>
