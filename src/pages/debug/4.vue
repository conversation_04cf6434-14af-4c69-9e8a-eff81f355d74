<template>
  <ly-default title="调试" title-position="left">
    <view class="common-com common-pd container">
      <zk-card
        icon="https://tzcjlw.oss-cn-shanghai.aliyuncs.com/mini-program/card/doc.png"
        title="人才学历申请"
      >
        <template #extra>
          <zk-tag type="danger"> 待提交 </zk-tag>
        </template>
        <template #default>
          <zk-des :schemas="schemas" :data="data"></zk-des>
        </template>
      </zk-card>

      <zk-upload-file value="ashkjdkjsdgas"></zk-upload-file>
      <zk-radio
        v-model:value="testvalueone"
        :options="[
          {
            label: '待核算',
            value: '0',
            title: '¥200',
          },
          {
            label: '已结算',
            value: '1',
            title: '¥200',
          },
        ]"
      ></zk-radio>

      <nut-button
        type="primary"
        @click="
          () => {
            testvalue = '2';
          }
        "
        >打开弹窗</nut-button
      >

      <zk-button-action
        :current="testvalue"
        :options="buttonOptions"
      ></zk-button-action>
      <zk-loading></zk-loading>
    </view>
  </ly-default>
</template>

<script setup lang="ts">
import type { ZkDesSchema } from "#/component";

const buttonOptions = [
  {
    value: "1",
    ok: () => {
      console.log("ok");
      testvalue.value = "2";
    },
    cancel: () => {
      console.log("cancel");
    },
    // ok按钮扩展
    okExtend: {
      type: "primary",
      text: "下一步",
    },
    cancelExtend: {
      type: "danger",
      text: "取消",
    },
  },
  {
    ok: () => {
      testvalue.value = "3";
    },
    cancel: () => {},
    // ok按钮扩展
    okExtend: {
      type: "primary",
      text: "下一步",
    },
    cancelExtend: {
      type: "primary",
      text: "上一步",
    },
    value: "2",
  },
  {
    ok: () => {
      testvalue.value = "4";
    },
    cancel: () => {},
    // ok按钮扩展
    okExtend: {
      type: "primary",
      text: "下一步",
    },
    cancelExtend: {
      type: "primary",
      text: "上一步",
    },
    value: "3",
  },
  {
    ok: () => {},
    cancel: () => {},
    // ok按钮扩展
    okExtend: {
      type: "primary",
      text: "提交",
    },
    cancelExtend: {
      type: "primary",
      text: "上一步",
    },
    value: "4",
  },
];

const schemas: ZkDesSchema[] = [
  {
    field: "test",
    label: "标题",
    show: true,
  },
  {
    field: "test2",
    label: "标题2",
    render: (v, data) => {
      console.log(v, data);
      return v;
    },
    show: (v, data) => {
      return true;
    },
  },
];
const data = {
  test: "测试数据",
  test2: "2026-01-02 22:22:22",
};

const test = `dsahdasdg
 sdjiashdas是打卡活技按时打算看见恒大盛京打卡活技按时打算看见恒大盛京
   asdhaskd
    sdjiashdas是打卡活技按时打算看见恒大盛京打卡活技按时打算看见恒大盛京
   asdhask
`;

const openvalue = ref(false);
const testvalue = ref("1");
const testvalueone = ref("0");
const current1 = ref("0");
const handleClickStep = (index: string) => {
  console.log(index);
  current1.value = index;
};
</script>

<style scoped lang="scss">
.container {
  // 样式区域
}
.test {
  // 文本原本内容布局渲染
}
</style>
