<template>
  <ly-default title="调试" title-position="left">
    <view class="common-com common-pd container">
      <zk-upload-image v-model:value="current1"></zk-upload-image>
      <uqrcode canvas-id="test" value="http"></uqrcode>
      <nut-button
        type="primary"
        @click="
          () => {
            openvalue = true;
          }
        "
        >打开弹窗</nut-button
      >
    </view>
  </ly-default>
</template>

<script setup lang="ts">
import uqrcode from "@/components/uqrcode/uqrcode.vue";
const openvalue = ref(false);

// const current1 = ref();
const current1 = ref(
  "http://**************:8080/sys/file/oss/file?fileName=c4609558d6c4457cb0dbb1907b9294d7.jpg",
);
</script>

<style scoped lang="scss">
.container {
  // 样式区域
}
.test {
  // 文本原本内容布局渲染
}
</style>
