<script setup lang="ts">
import { useMessage } from "wot-design-uni";
const message = useMessage();
const state = ref({
  file: "xxx",
});
const methods = {
  test() {
    message.alert({
      title: "测试",
      msg: "文字测试",
    });
  },
};
</script>

<template>
  <view>
    <!--    基础-->
    <nut-form ref="formRef2" :model-value="state">
      <nut-form-item
        prop="birthday"
        required
        label="缴纳社保记录"
        label-width="250rpx"
        label-align="left"
      >
      </nut-form-item>
    </nut-form>

    <!--  item-->
    <nut-form-item
      prop="nativePlace"
      required
      label="单位名称"
      label-align="left"
    >
    </nut-form-item>
    <!--  item label-->
    <nut-form-item
      prop="nativePlace"
      required
      label="单位名称"
      label-align="left"
    >
      <template #label>
        <zk-text size="32" color="#333">学历证明</zk-text>
      </template>
    </nut-form-item>

    <!--input-->
    <nut-input
      inputClass="custom-input"
      placeholderClass="custom-input-placeholder"
      placeholder="请输入"
      v-model:model-value="state.nativePlace"
    ></nut-input>

    <!--  data-->
    <zk-date-picker
      placeholder="选择日期"
      type="date"
      v-model:value="state.birthday"
    ></zk-date-picker>

    <!--  radio-->
    <view class="custom-right">
      <nut-radio-group
        direction="horizontal"
        placeholderClass="custom-input-placeholder"
        v-model:model-value="state.sex"
      >
        <nut-radio label="1">男</nut-radio>
        <nut-radio label="2">女</nut-radio>
      </nut-radio-group>
    </view>

    <!--    select-->
    <zk-picker
      :options="[
        {
          value: 1,
          text: '健康',
        },
        {
          value: 2,
          text: '轻微疾病',
        },
        {
          value: 3,
          text: '重大疾病',
        },
      ]"
      placeholder="请选择"
    ></zk-picker>

    <!--    tips-->
    <view>
      <zk-text color="#ccc"> 提示信息 </zk-text>
    </view>

    <!--    文件-->

    <zk-upload-file
      title="上传附件"
      v-model:value="state.file"
    ></zk-upload-file>

    <nut-button type="primary" @click="methods.test()">测试</nut-button>
    <wd-message-box></wd-message-box>
  </view>
</template>

<style scoped lang="scss"></style>
