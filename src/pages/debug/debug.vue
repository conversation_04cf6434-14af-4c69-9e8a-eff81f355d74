<template>
  <ly-default title="调试" title-position="left">
    <view class="common-com common-pd container">
      <zk-card
        icon="https://tzcjlw.oss-cn-shanghai.aliyuncs.com/mini-program/card/doc.png"
        title="人才学历申请"
      >
        <template #extra>
          <zk-tag type="danger"> 待提交 </zk-tag>
        </template>
        <template #default>
          <zk-des :schemas="schemas" :data="data"></zk-des>
        </template>
      </zk-card>
      <zk-affirm
        v-model:open="openvalue"
        @on-ok="
          () => {
            console.log('确定回调');
          }
        "
        @on-cancel="
          () => {
            console.log('取消回调');
          }
        "
        :text="test"
      ></zk-affirm>
      <zk-card>
        <view>
          <zk-steps-one
            v-model:current="testvalueone"
            status="wait"
            :options="[
              {
                label: '基本信息',
                value: '0',
                des: '申请人：刘志芳',
              },
              {
                label: '学历信息',
                value: '1',
                des: '结果：不通过',
              },
              {
                label: '2',
                value: '2',
              },
              {
                label: '3',
                value: '3',
              },
            ]"
          ></zk-steps-one>
        </view>
      </zk-card>

      <nut-button
        type="primary"
        @click="
          () => {
            openvalue = true;
          }
        "
        >打开弹窗</nut-button
      >
    </view>
  </ly-default>
</template>

<script setup lang="ts">
import type { ZkDesSchema } from "#/component";

const schemas: ZkDesSchema[] = [
  {
    field: "test",
    label: "标题",
    show: true,
  },
  {
    field: "test2",
    label: "标题2",
    render: (v, data) => {
      console.log(v, data);
      return v;
    },
    show: (v, data) => {
      return true;
    },
  },
];
const data = {
  test: "测试数据",
  test2: "2026-01-02 22:22:22",
};

const test = `dsahdasdg
 sdjiashdas是打卡活技按时打算看见恒大盛京打卡活技按时打算看见恒大盛京
   asdhaskd
    sdjiashdas是打卡活技按时打算看见恒大盛京打卡活技按时打算看见恒大盛京
   asdhask
`;

const openvalue = ref(false);
const testvalue = ref("1");
const testvalueone = ref("0");
const current1 = ref("0");
const handleClickStep = (index: string) => {
  console.log(index);
  current1.value = index;
};
</script>

<style scoped lang="scss">
.container {
  // 样式区域
}
.test {
  // 文本原本内容布局渲染
}
</style>
