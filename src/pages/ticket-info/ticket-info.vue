<template>
  <ly-info title="核销详情" title-position="left">
    <view class="common-com common-pd container">
      <view class="container-top container-center">
        <nut-icon size="45rpx" :name="ticketstatus.icon"></nut-icon>
        <zk-text color="#FFF" size="40"
          ><text>
            {{ ticketstatus.text }}
          </text></zk-text
        >
      </view>

      <view class="container-info">
        <view class="container-info-line"> </view>
        <view class="container-info-body">
          <view class="container-info-top">
            <view>
              <zk-text size="48" color="#1677FF">
                <text>
                  {{ couponTitle }}
                </text>
              </zk-text>
            </view>
            <view>
              <zk-text size="36" color="#333">
                <text>
                  {{ get(pathData, "operationStore.title") }}
                </text>
              </zk-text>
            </view>
          </view>
          <view class="container-info-bottom">
            <zk-des :schemas="desSchemas" :data="pathData"></zk-des>
          </view>
        </view>
      </view>
    </view>
  </ly-info>
</template>

<script setup lang="ts">
import { usePathData } from "@/hook/usePathData";
import { get } from "lodash-es";
const { eventData: pathData } = usePathData("pathData");
const desSchemas = [
  {
    field: "id",
    label: "核销券码",
  },
  {
    field: "useTime",
    label: "核销时间",
  },
];

// 券状态
const ticketstatus = computed(() => {
  const state = get(unref(pathData), "state");
  if (state === "USE") {
    return {
      icon: "checked",
      text: "消费券已核销",
    };
  } else if (state === "OVERDUE") {
    return {
      icon: "mask-close",
      text: "消费券已过期",
    };
  } else {
    return {
      icon: "mask-close",
      text: "系统错误",
    };
  }
});

// 满减信息
const couponTitle = computed(() => {
  const price = get(unref(pathData), "operationCoupon.price", "--");
  const threshold = get(unref(pathData), "operationCoupon.threshold", "--");
  return `满${threshold}减${price}`;
});
</script>

<style scoped lang="scss">
.container {
  &-info {
    background: #ffffff;
    height: 446rpx;
    flex-shrink: 0;
    margin: 78rpx 24rpx 0 24rpx;
    border-radius: 0 0 16rpx 16rpx;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 9;
    &-top {
      height: 263rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-bottom: #cccccc 1rpx dashed;
      box-sizing: border-box;
      margin: 0 24rpx;
    }

    &-bottom {
      display: flex;
      flex-direction: column;

      flex: 1;
      justify-content: center;
      margin: 0 24rpx;
    }

    &-line {
      position: absolute;
      top: -16rpx;
      left: -16rpx;
      width: calc(100% + 32rpx);
      border-radius: 100px;
      background: #0551a7;
      height: 34rpx;
      z-index: 1;
    }
    &-body {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 10;
      background: #fff;
      display: flex;
      justify-content: space-between;
      flex-direction: column;
    }
  }
  // 样式区域
  &-top {
    color: #fff;
    margin-top: 72rpx;
    display: flex;
    gap: 10rpx;
  }
  &-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
