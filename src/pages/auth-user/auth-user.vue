<script setup lang="ts">
import { apiMemberAuth } from '@/api/user'
import { useApiLoading } from '@/hook/useApiLoading'
import { EducationArray } from '@/maps/op'
import { goHome } from '@/router/topage'
import { useUserStore } from '@/store/user'
import { get } from 'lodash-es'
import { useMessage } from 'wot-design-uni'

const message = useMessage()
const userStore = useUserStore()
const { loading, reload } = useApiLoading({
  api: apiMemberAuth,
  immediate: false,
  toastLoading: true,
})

interface State {
  name?: string
  idCard?: string
  memberPhone?: string
  enterpriseName?: string
  education?: string
  certificateAttach?: string
  socialSecurityAttach?: string
}

const state = reactive<State>({
  name: undefined,
  idCard: undefined,
  memberPhone: undefined,
  enterpriseName: undefined,
  education: undefined,
  certificateAttach: undefined,
  socialSecurityAttach: undefined,
})

const formBaseRef = ref()
const formImageRef = ref()
const formSocialSecurityRef = ref()

const methods = {
  tips(_tip: string) {
    uni.showToast({
      title: _tip,
      icon: 'none',
      duration: 2000,
    })
  },
  async submit() {
    // 开始校验
    const { validate: basicValidate } = await formBaseRef.value!
    const { validate: imageValidate } = await formImageRef.value!
    const { validate: socialSecurityValidate } = await formSocialSecurityRef.value!

    // 同时校验两个
    const [basicV, imageV, socialV] = await Promise.all([
      basicValidate(),
      imageValidate(),
      socialSecurityValidate(),
    ])

    if (basicV.valid && imageV.valid && socialV.valid) {
      console.log(state)
      reload(state).then(() => {
        userStore.refreshUserInfo()
        message
          .alert({
            title: '提交成功',
            msg: '请耐心等待审核',
          })
          .finally(() => {
            goHome()
          })
      }).catch((err) => {
        if (err.code === 1 && err.msg) {
          message.show({ msg: err.msg, showErr: true, title: '提示' })
        }
      })
    }
  },
  setDefault() {
    if (!userStore.operationAudit) {
      state.memberPhone = userStore.phone || undefined
      return
    }
    state.name = get(userStore.operationAudit, 'name')
    state.idCard = get(userStore.operationAudit, 'idCard')
    state.memberPhone = get(userStore.operationAudit, 'memberPhone')
    state.enterpriseName = get(userStore.operationAudit, 'enterpriseName')
    state.education = get(userStore.operationAudit, 'education')
    state.certificateAttach = get(
      userStore.operationAudit,
      'certificateAttach',
    )
    state.socialSecurityAttach = get(
      userStore.operationAudit,
      'socialSecurityAttach',
    )
  },
}
methods.setDefault()
</script>

<template>
  <ly-default title="完善个人信息认证" title-position="left">
    <view class="common-com common-pd container">
      <nut-form ref="formBaseRef" :model-value="state">
        <nut-form-item
          prop="name"
          required
          label="姓名"
          label-align="left"
          :rules="[
            { required: true, message: '请输入' },
            {
              message: '最多20个字',
              regex: /^.{0,19}$/,
            },
          ]"
        >
          <nut-input
            v-model:model-value="state.name"
            input-class="custom-input"
            placeholder-class="custom-input-placeholder"
            placeholder="请输入"
          />
        </nut-form-item>
        <nut-form-item
          prop="idCard"
          required
          label="身份证号"
          label-align="left"
          :rules="[{ required: true, message: '请输入' }]"
        >
          <nut-input
            v-model:model-value="state.idCard"
            input-class="custom-input"
            placeholder-class="custom-input-placeholder"
            placeholder="请输入"
          />
        </nut-form-item>
        <nut-form-item
          prop="memberPhone"
          required
          label="本人电话"
          label-align="left"
          :rules="[{ required: true, message: '请输入' }]"
        >
          <nut-input
            v-model:model-value="state.memberPhone"
            input-class="custom-input"
            placeholder-class="custom-input-placeholder"
            placeholder="请输入"
            disabled
            @click="methods.tips('号码不支持修改')"
          />
        </nut-form-item>
        <nut-form-item
          prop="enterpriseName"
          required
          label="单位名称(与社保单位一致)"
          label-width="350rpx"
          label-align="left"
          :rules="[
            { required: true, message: '请输入' },
            {
              message: '最多20个字',
              regex: /^.{0,49}$/,
            },
          ]"
        >
          <nut-input
            v-model:model-value="state.enterpriseName"
            input-class="custom-input"
            placeholder-class="custom-input-placeholder"
            placeholder="请输入"
          />
        </nut-form-item>
        <nut-form-item
          prop="education"
          required
          label="学历"
          label-align="left"
          :rules="[{ required: true, message: '请选择' }]"
        >
          <zk-picker
            v-model:value="state.education"
            :options="EducationArray"
            placeholder="请选择"
          />
        </nut-form-item>
      </nut-form>

      <nut-form ref="formImageRef" :model-value="state">
        <nut-form-item
          label-position="top"
          label-align="left"
          prop="certificateAttach"
        >
          <template #label>
            <zk-text size="32" color="#333">
              学历证明
            </zk-text>
          </template>
          <div class="flex flex-col gap-1">
            <zk-text color="#666">
              请提供学信网的学历毕业证书，如有多张文件请合并一起上传
              <!-- 请提供学信网的学历毕业证书 -->
            </zk-text>
            <zk-upload-image
              v-model:value="state.certificateAttach"
              title="请上传学信网证书"
            />
          </div>
        </nut-form-item>
      </nut-form>

      <nut-form ref="formSocialSecurityRef" :model-value="state">
        <nut-form-item
          label-position="top"
          label-align="left"
          prop="socialSecurityAttach"
          :rules="[{ required: true, message: '请选择您的缴纳社保记录' }]"
        >
          <template #label>
            <zk-text size="32" color="#333">
              缴纳社保记录
            </zk-text>
          </template>
          <div class="flex flex-col gap-1">
            <zk-text color="#666">
              缴纳社保记录需提供在通州湾起缴至今的记录
            </zk-text>
            <zk-upload-image
              v-model:value="state.socialSecurityAttach"
              title="请上传社保附件"
            />
          </div>
        </nut-form-item>
      </nut-form>
    </view>
    <template #bottom>
      <view class="common-pd">
        <nut-button
          :loading="loading"
          type="primary"
          block
          @click="methods.submit()"
        >
          提交
        </nut-button>
      </view>
    </template>
    <wd-message-box />
  </ly-default>
</template>

<style scoped lang="scss">
.container {
  // 样式区域
}
</style>
