<template>
  <ly-default title="资讯详情" title-position="left">
    <view class="common-com common-pd container">
      <view class="container-box">
        <view class="container-title">
          <zk-text size="40" bold :text="get(eventData, 'title', '----')">
          </zk-text>
        </view>
        <view class="container-time">
          <zk-text>
            <!--            时间-->
            <text color="#999" :text="get(eventData, 'updateTime', '----')">
            </text>
          </zk-text>
        </view>
        <image
          class="container-image"
          mode="aspectFill"
          :src="get(eventData, 'photo')"
          v-if="false"
        >
        </image>
        <view class="container-content">
          <rich-text :nodes="nodesData"></rich-text>
        </view>
      </view>
    </view>
  </ly-default>
</template>

<script setup lang="ts">
import { usePathData } from "@/hook/usePathData";
import { get } from "lodash-es";

const { eventData } = usePathData("pageData");
const nodesData = computed(() => {
  const text = get(eventData.value, "detail", "");
  if (!text) return "";
  return text.replace(/<(\/)?pre[^>]*>/gi, "");
});
</script>

<style scoped lang="scss">
.container {
  padding-top: 20rpx;
  // 样式区域
  &-box {
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    flex: 1;
  }
  &-image {
    margin-top: 24rpx;
    height: 254rpx;
    border-radius: 16rpx;
    overflow: hidden;
    width: 100%;
  }
  &-title {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20rpx;
    // 文字换行
    word-break: break-all;
    // 内容有换行允许换行
    word-wrap: break-word;
  }
  &-time {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10rpx;
  }
  &-content {
    margin-top: 24rpx;
    // 文字换行
    word-break: break-all;
    // 内容有换行允许换行
    word-wrap: break-word;
    font-size: 24rpx;
    color: #333;
  }
}
</style>

<style>
[alt] {
  max-width: 100%;
  height: auto;
}
</style>
