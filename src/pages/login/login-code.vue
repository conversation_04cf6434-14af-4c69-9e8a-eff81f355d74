<script setup lang="ts">
import { get } from "lodash-es";
import { apiLoginApi } from "@/api/login";
import { useLoginStore } from "@/store/login";
import LoginTitle from "@/pages/login/login-title.vue";

interface Props {
  isAgree: boolean;
  clientId: string;
}
const props = defineProps<Props>();
const loginStore = useLoginStore();
const emit = defineEmits(["onLoginType", "onLogin"]);

const methods = {
  getPhoneNumber(params: any) {
    const detail = get(params, "detail", {});
    console.log("手机号 code", detail);
    const errMsg = get(detail, "errMsg", "");
    if (errMsg === "getPhoneNumber:fail user deny") {
      uni.showToast({
        title: "用户取消授权",
        icon: "none",
      });
      return;
    }

    if (errMsg !== "getPhoneNumber:ok") {
      uni.showToast({
        title: "获取手机号失败",
        icon: "none",
      });
      return;
    }
    emit("onLogin", {
      code: get(detail, "code", ""),
    });
    // methods.loginByMobile(get(detail, "code", ""));
  },
  // async loginByMobile(code: any) {
  //   uni.showLoading({
  //     title: "登录中",
  //     mask: true,
  //     // 不关
  //     duration: 0,
  //   });
  //   try {
  //     const res = await apiLoginApi(
  //       {
  //         code,
  //         grant_type: "mini_code",
  //         scope: "server",
  //       },
  //       {
  //         Authorization: `Basic bWluaTptaW5p`,
  //       },
  //     );
  //     uni.hideLoading();
  //     uni.showToast({
  //       title: "登录成功",
  //     });
  //     console.log("登录成功", res);
  //     loginStore.setToken(get(res, "accessToken.tokenValue", ""));
  //     loginStore.setClientId("mini");
  //     // await userStore.refreshUserInfo();
  //     // methods.handleClickLeft();
  //   } catch (e) {
  //     uni.hideLoading();
  //     uni.showToast({
  //       title: "登录失败",
  //     });
  //   }
  // },

  // 提示
  showTip() {
    uni.showToast({
      title: "请先同意用户协议",
      icon: "none",
    });
  },

  // 修改登录类型
  changeLoginType() {
    emit("onLoginType", "mini_sms");
  },
};
</script>

<template>
  <view class="login-code">
    <view class="login-code-title">
      <LoginTitle :clientId="clientId"></LoginTitle>
    </view>
    <view class="login-code-block">
      <view v-if="isAgree">
        <nut-button
          type="primary"
          open-type="getPhoneNumber"
          block
          size="large"
          @getphonenumber="methods.getPhoneNumber"
        >
          手机号一键登录
        </nut-button>
      </view>
      <view v-else>
        <nut-button
          @click="methods.showTip()"
          type="primary"
          block
          size="large"
        >
          手机号一键登录
        </nut-button>
      </view>

      <view>
        <nut-button
          custom-class="login-wechat-code"
          block
          plain
          size="large"
          type="default"
          @click="methods.changeLoginType"
        >
          手机验证码登录
        </nut-button>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.login-code {
  &-title {
    display: flex;
    flex-direction: column;
    margin-top: 112rpx;
  }
  &-block {
    margin-top: 90rpx;
    display: flex;
    flex-direction: column;
    gap: 40rpx;
  }
}
</style>
