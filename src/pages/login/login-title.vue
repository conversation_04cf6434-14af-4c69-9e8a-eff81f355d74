<script setup lang="ts">
interface Props {
  clientId: string;
}
defineProps<Props>();
</script>

<template>
  <view class="login-title">
    <view class="login-title-center">
      <view class="login-title-right-text">才聚蓝湾</view>
      <view class="login-title-right">
        <view class="login-title-right-text">悦享生活</view>
      </view>
    </view>
    <view class="login-title-store" v-if="clientId === 'store'">
      欢迎来到商家端
    </view>
  </view>
</template>

<style scoped lang="scss">
.login-title {
  color: #000;
  font-size: 50rpx;
  display: flex;
  flex-direction: column;
  height: 340rpx;
  justify-content: space-between;

  &-center {
    display: flex;
    justify-content: center;
    gap: 6rpx;
  }

  &-store {
    display: flex;
    justify-content: center;
    transform: skewX(-5deg);
  }

  &-right {
    border-radius: 100rpx;
    background: #1677ff;
    color: #fff;
    padding: 3rpx 25rpx;
    &-text {
      font-size: 52rpx;
      // 字体微微倾斜
      transform: skewX(-5deg);
      // 字体间隙
      letter-spacing: 4rpx;
    }
  }
}
</style>
