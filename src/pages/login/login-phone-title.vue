<script setup lang="ts">
interface Props {
  clientId: string;
}
defineProps<Props>();
</script>

<template>
  <view class="login-phone-title">
    <view>您好👋 </view>
    <view v-if="clientId === 'mini'">欢迎登录</view>
    <view v-else>欢迎来到商家端</view>
  </view>
</template>

<style scoped lang="scss">
.login-phone-title {
  color: #000;
  font-size: 50rpx;
  height: 124rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
</style>
