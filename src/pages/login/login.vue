<template>
  <view class="login">
    <wd-navbar
      custom-style="background-color: transparent !important"
      placeholder
      safeAreaInsetTop
      :bordered="false"
      left-arrow
      @click-left="methods.handleClickLeft"
    >
    </wd-navbar>
    <image
      class="login-backimg"
      src="https://tzcjlw.oss-cn-shanghai.aliyuncs.com/mini-program/login-back-top.png"
    ></image>
    <view class="login-container">
      <LoginCode
        :is-agree="isAgree"
        @on-login-type="methods.setLoginType"
        v-if="loginType === 'mini_code'"
        :clientId="clientId"
        @onLogin="methods.onLogin"
      ></LoginCode>
      <LoginPhone
        :clientId="clientId"
        :is-agree="isAgree"
        v-if="loginType === 'mini_sms'"
        @onLogin="methods.onLogin"
      ></LoginPhone>
    </view>
    <view class="login-agree">
      <radio
        style="transform: scale(0.7)"
        @click="methods.agree()"
        :checked="isAgree === true"
      />
      <wd-text
        text="我已阅读并同意"
        size="small"
        @click="methods.agree()"
      ></wd-text>

      <template v-if="clientId === 'mini'">
        <wd-text
          @click="goUserAgreement()"
          type="primary"
          text="《用户协议》"
          size="small"
        ></wd-text>
        <wd-text text="、" size="small"></wd-text>
        <wd-text
          @click="goPrivacyPolicy()"
          type="primary"
          text="《用户隐私政策》"
          size="small"
        ></wd-text>
      </template>
      <template v-else>
        <wd-text
          @click="goStoreAgreement()"
          type="primary"
          text="《商家协议》"
          size="small"
        ></wd-text>
        <wd-text text="、" size="small"></wd-text>
        <wd-text
          @click="goStorePrivacyPolicy()"
          type="primary"
          text="《商家隐私政策》"
          size="small"
        ></wd-text>
      </template>
    </view>
    <view class="login-footer">
      <view class="safe-area">
        <wd-text
          @click="methods.setClientType('store')"
          type="primary"
          text="我是商家 >"
          v-if="clientId === 'mini'"
        ></wd-text>
        <wd-text
          v-else
          @click="methods.setClientType('mini')"
          type="primary"
          text="我是人才 >"
        ></wd-text>
      </view>
    </view>

    <wd-message-box selector="agree-box-slot">
      <view>
        <wd-text text="请已阅读并同意" size="small"></wd-text>
        <wd-text
          @click="goUserAgreement()"
          type="primary"
          text="《用户协议》"
          size="small"
        ></wd-text>
        <wd-text text="、" size="small"></wd-text>
        <wd-text
          @click="goPrivacyPolicy()"
          type="primary"
          text="《隐私政策》"
          size="small"
        ></wd-text>
      </view>
    </wd-message-box>
    <zk-env-watermark />
  </view>
</template>

<script setup lang="ts">
import {
  goUserAgreement,
  goPrivacyPolicy,
  goStoreAgreement,
  goStorePrivacyPolicy,
} from "@/router/topage";
import LoginCode from "@/pages/login/login-code.vue";
import LoginPhone from "@/pages/login/login-phone.vue";
import { useLoginStore } from "@/store/login";
import type { ClientId } from "#/login";
type LoginType = "mini_code" | "mini_sms";

const isAgree = ref(false);

const loginType = ref<LoginType>("mini_code");
const clientId = ref<ClientId>("mini");
const loginStore = useLoginStore();

const methods = {
  async onLogin(params: Record<string, any>) {
    // params 是登录参数
    await loginStore.login({
      clientId: clientId.value,
      grant_type: loginType.value,
      ...params,
    });
  },
  agree() {
    isAgree.value = !isAgree.value;
  },
  handleClickLeft() {
    // 判断是否是手机号登录页面
    if (loginType.value === "mini_sms") {
      loginType.value = "mini_code";
      return;
    }
    // 判断是否是最后一页
    let pages = getCurrentPages();
    if (pages.length <= 1) {
      methods.goHome();
      return;
    }
    uni.navigateBack();
  },
  goHome() {
    console.log("goHome");
    uni.reLaunch({
      url: "/pages/index/index",
    });
  },

  setLoginType(type: LoginType) {
    loginType.value = type;
  },
  setClientType(v: ClientId) {
    clientId.value = v;
    // 切换的时候
    isAgree.value = false;
  },
};
</script>

<style scoped lang="scss">
.login {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(203deg, #d0e2fa -34.56%, #fff 81%);
  position: relative;
  &-backimg {
    top: 0;
    right: 0;
    position: absolute;
    width: 500rpx;
    z-index: 0;
  }

  &-container {
    padding: 0 40rpx;
    box-sizing: border-box;
    z-index: 1;
  }

  &-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #999;
    font-size: 24rpx;
    position: absolute;
    bottom: 100rpx;
    width: 100%;
  }
  &-agree {
    margin-top: 60rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

:deep(.nut-input--border) {
  border-radius: 100rpx;
  overflow: hidden;
}
</style>

<style lang="scss">
.login-input {
  height: 55rpx;
}
.login-placeholder {
  color: #cccccc;
  font-size: 32rpx;
  font-weight: lighter;
}

.login-wechat-code {
  background: transparent !important;
}
</style>
