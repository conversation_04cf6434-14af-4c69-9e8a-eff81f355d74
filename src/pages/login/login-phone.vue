<script setup lang="ts">
import LoginPhoneTitle from "@/pages/login/login-phone-title.vue";
import { useLoginStore } from "@/store/login";
import { smsSendApi } from "@/api/login";
const loginStore = useLoginStore();
interface Props {
  isAgree: boolean;
  clientId: string;
}
const props = defineProps<Props>();
const emit = defineEmits(["onLogin"]);
const basicData = reactive({
  mobile: "",
  code: "",
});

// 验证码倒计时展示
const showCountdown = ref(false);

const methods = {
  // 发送验证码
  async sendCode() {
    if (!props.isAgree) {
      methods.showTip("请先同意协议");
      return;
    }
    if (!basicData.mobile) {
      methods.showTip("请输入手机号");
      return;
    }
    // TODO: 发送验证码
    await smsSendApi(toRaw(basicData));
    loginStore.refreshSendsmsTime();
    showCountdown.value = true;
  },

  // 登录
  login() {
    if (!props.isAgree) {
      methods.showTip("请先同意协议");
      return;
    }
    if (!basicData.mobile) {
      methods.showTip("请输入手机号");
      return;
    }
    if (!basicData.code) {
      methods.showTip("请输入验证码");
      return;
    }
    emit("onLogin", toRaw(basicData));
  },
  // 提示
  showTip(msg: string) {
    uni.showToast({
      title: msg,
      icon: "none",
    });
  },
  tipSendTime() {
    console.log("tipSendTime");
    // 计算还有多少s 可以发送
    const now = Date.now();
    const time = Math.floor((loginStore.sendsmsTime - now) / 1000);
    methods.showTip("请等待" + time + "秒后再次发送");
  },
};

onMounted(() => {
  // 从内存获取上次发送验证码时间
  const lastSendTime = loginStore.sendsmsTime;
  if (lastSendTime) {
    // 计算当前时间和上次时间 如果当前时间小于发送倒计时截止时间就展示
    const now = Date.now() + 1000;
    if (now < lastSendTime) {
      showCountdown.value = true;
    }
  }
});

// @click.native="methods.tipSendTime()"
</script>

<template>
  <view class="login-phone">
    <view class="login-phone-title">
      <LoginPhoneTitle :clientId="clientId" />
    </view>
    <view class="login-phone-block">
      <nut-input
        v-model="basicData.mobile"
        placeholder="请输入手机号码"
        placeholder-class="login-placeholder"
        max-length="11"
        input-class="login-input"
        type="text"
      />

      <nut-input
        v-model="basicData.code"
        placeholder="请输入验证码"
        max-length="10"
        placeholder-class="login-placeholder"
        input-class="login-input"
        type="text"
      >
        <template #right>
          <view class="login-phone-sendText">
            <view v-if="showCountdown" @click="methods.tipSendTime()">
              <nut-countdown
                :end-time="loginStore.sendsmsTime"
                format="ss 秒"
                @on-end="showCountdown = false"
              ></nut-countdown>
            </view>
            <view v-else @click="methods.sendCode()">发送验证码</view>
          </view>
        </template>
      </nut-input>

      <nut-button @click="methods.login()" type="primary" block size="large">
        登录
      </nut-button>
    </view>
  </view>
</template>

<style scoped lang="scss">
.login-phone {
  &-title {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-top: 70rpx;
  }
  &-block {
    margin-top: 90rpx;
    display: flex;
    flex-direction: column;
    gap: 40rpx;
  }
  &-sendText {
    color: #1677ff;
  }
}
</style>
