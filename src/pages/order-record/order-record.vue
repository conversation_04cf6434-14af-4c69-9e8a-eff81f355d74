<template>
  <view class="order-record">
    <scroll-view
      :scroll-y="true"
      class="order-record-scroll"
      :refresher-enabled="true"
      @refresherrefresh="onPullDownRefresh"
      :refresher-triggered="refresherStatus"
      @scrolltolower="onReachBottom"
    >
      <view class="order-record-empty" v-if="list.length === 0">
        <wd-status-tip image="content" tip="暂无内容" />
      </view>

      <OrderCard
        v-for="item in list"
        :title="item.name"
        :key="item.id"
        :order-info="item"
      ></OrderCard>
      <view class="order-record-bock safe-area">
        <view class="order-record-status">
          <wd-loading v-if="status === 'loading'" size="35rpx" />
          <wd-text
            v-if="status === 'loading'"
            type="primary"
            text="加载中..."
          ></wd-text>
          <wd-text
            v-if="status === 'nomore'"
            type="warning"
            text="没有更多"
          ></wd-text>
          <wd-text
            v-if="status === 'loadmore'"
            type="primary"
            text="下滑加载更多"
          ></wd-text>
          <wd-text
            v-if="status === 'error'"
            type="error"
            text="加载失败"
          ></wd-text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { Pagination } from "@/utils/pagination";
import { orderPage } from "@/api/op";
import OrderCard from "@/pages/order-record/order-card.vue";
const status = ref("loadmore");
const pagination = new Pagination(orderPage);
const list = ref<any>([]);
const refresherStatus = ref(false);
const onPullDownRefresh = async () => {
  try {
    status.value = "loading";
    refresherStatus.value = true;
    let res = await pagination.refresh();
    setItemList(res);
    // 获取分页
    const hasNextPage = pagination.hasNextPage();
    if (hasNextPage) {
      status.value = "nomore";
    } else {
      status.value = "loadmore";
    }
  } catch (error) {
    status.value = "error";
  } finally {
    refresherStatus.value = false;
  }
};

const onReachBottom = async () => {
  if (status.value === "nomore") {
    return;
  }
  try {
    status.value = "loading";
    let res = await pagination.nextPage();
    setItemList(res);
    const hasNextPage = pagination.hasNextPage();
    if (hasNextPage) {
      status.value = "nomore";
    } else {
      status.value = "loadmore";
    }
  } catch (error) {
    console.log(error);
    status.value = "error";
  } finally {
    // 获取分页

    refresherStatus.value = false;
  }
};

function setItemList(_list: any[]) {
  console.log(_list);
  list.value = _list;
}

nextTick(() => {
  // 手动触发下拉刷新
  // uni.startPullDownRefresh();
  refresherStatus.value = true;
});
</script>

<style scoped lang="scss">
.order-record {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  height: 100%;

  &-scroll {
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 10rpx;
    padding-top: 20rpx;
  }
  &-bock {
    height: 200rpx;
  }
  &-status {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30rpx;
    gap: 20rpx;
    height: 80rpx;
  }
  &-empty {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin-top: -50px;
  }
}
</style>
