<script setup lang="ts">
import { get } from "lodash-es";
import { matchMap } from "@/utils/tools";
import { OrderTypeArray } from "@/maps/op";

interface Props {
  orderInfo: any;
}
const props = defineProps<Props>();

const hospitalName = computed(() => {
  return get(props.orderInfo, "bizHospital.name", "");
});

const serverName = computed(() => {
  return get(props.orderInfo, "bizProduct.name", "");
});

const remark = computed(() => {
  return get(props.orderInfo, "remark", "");
});
const mobile = computed(() => {
  return get(props.orderInfo, "bizHospital.mobile", "");
});

const createTime = computed(() => {
  return get(props.orderInfo, "createTime", "");
});
const count = computed(() => {
  const _count = get(props.orderInfo, "count", 0) || 0;
  return `${_count} 个`;
});

const typeReander = computed(() => {
  const typeOp = matchMap(get(props.orderInfo, "type"), OrderTypeArray);
  return typeOp;
});

const section = computed(() => {
  return get(props.orderInfo, "bizSection.name", "--");
});
</script>

<template>
  <view class="order-card">
    <wd-card>
      <template #title>
        <view class="order-card-title">
          {{ hospitalName }}
        </view>
      </template>
      <view class="order-card-info">
        <view> 预约服务：{{ serverName }} </view>
        <view> 商家电话：{{ mobile }} </view>
        <view> 预约类型：{{ typeReander.label }} </view>
        <view v-if="get(orderInfo, 'type') === 1">
          床位：{{ get(orderInfo, "bed", "--") }}
        </view>
        <view> 科室：{{ section }} </view>
        <view> 购买数量：{{ count }} </view>
        <view> 备注信息：{{ remark }} </view>
        <view> 创建时间：{{ createTime }} </view>
      </view>
    </wd-card>
  </view>
</template>

<style scoped lang="scss">
.order-card {
  &-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
  &-info {
    font-size: 30rpx;
    display: flex;
    flex-direction: column;
    gap: 10rpx;
    color: #506a8d;
  }
}
</style>
