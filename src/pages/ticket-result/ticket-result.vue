<template>
  <ly-default title="核销结果" title-position="left">
    <view class="common-com common-pd container">
      <view class="container-card">
        <view class="container-card-empty">
          <zk-empty
            :status="status"
            :title="showstatus"
            :des="showcode"
          ></zk-empty>
        </view>

        <view class="container-card-action">
          <view class="button">
            <nut-button block @click="goHome('Ticket')">
              查看核销记录
            </nut-button>
          </view>
          <view class="button">
            <nut-button block type="primary" @click="goHome()">
              返回首页
            </nut-button>
          </view>
        </view>
      </view>
    </view>
  </ly-default>
</template>

<script setup lang="ts">
import { goHome } from "@/router/topage";
import { onLoad } from "@dcloudio/uni-app";
import { decodeObjectRecursive } from "@/utils/querytool";
import { get } from "lodash-es";

const status = ref("success");
const code = ref("");
const showcode = computed(() => {
  return `券码 ${code.value}`;
});

const showstatus = computed(() => {
  if (status.value === "success") {
    return "消费券核销成功";
  } else {
    return "消费券核销失败";
  }
});

onLoad((option) => {
  const params = decodeObjectRecursive(option);
  status.value = get(params, "status", "success");
  code.value = get(params, "code", "");
});
</script>

<style scoped lang="scss">
.container {
  // 样式区域
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  &-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    margin: 50rpx 24rpx 24rpx 24rpx;
    background: #fff;
    padding: 0 24rpx;
    border-radius: 16rpx;
    &-empty {
      margin-top: 184rpx;
    }
    &-action {
      margin-top: 80rpx;
      display: flex;
      gap: 24rpx;
      & .button {
        flex: 1;
        flex-shrink: 0;
      }
    }
  }
}
</style>
