<template>
  <ly-default title="可使用的门店" title-position="left">
    <view class="common-com common-pd container">
      <scroll-view
        :scroll-y="true"
        class="common-scroll"
        :refresher-enabled="true"
        @refresherrefresh="onPullDownRefresh"
        :refresher-triggered="refresherStatus"
        @scrolltolower="onReachBottom"
      >
        <view class="common-list container-list">
          <zk-card-store
            v-for="(item, index) in storeList"
            :url="item.photo"
            :address="item.address"
            :name="item.title"
            :key="index"
            :phone="item.phone"
            @call="methods.call"
          ></zk-card-store>
        </view>

        <view class="common-empty" v-if="isEmpty">
          <wd-status-tip image="content" tip="暂无内容" />
        </view>
        <zk-loadmore :status="status" />
        <zk-loading :loading="loading"></zk-loading>
      </scroll-view>
    </view>
    <wd-message-box />
  </ly-default>
</template>

<script setup lang="ts">
import { apiCouponStorePageList } from "@/api/coupon";
import { usePathData } from "@/hook/usePathData";
import { useScrollView } from "@/hook/useScrollView";
import { useMessage } from "wot-design-uni";
import { get } from "lodash-es";

const message = useMessage();
const { params } = usePathData("", () => {
  reload();
});
const {
  reload,
  list: storeList,
  isEmpty,
  status,
  onPullDownRefresh,
  refresherStatus,
  onReachBottom,
  loading,
} = useScrollView({
  api: apiCouponStorePageList,
  params,
  immediate: false,
});

const methods = {
  call(phone: string) {
    console.log("call", phone);
    message
      .confirm({
        title: "拨号提示",
        msg: `是否拨打${phone}`,
      })
      .then(() => {
        uni
          .makePhoneCall({
            phoneNumber: phone,
          })
          .catch((err) => {
            if (get(err, "errMsg") === "makePhoneCall:fail cancel") {
              message.alert({
                title: "拨打已取消",
                msg: `号码：${phone}`,
              });
              return;
            }
            message.alert({
              title: "拨号失败",
              msg: `号码：${phone}`,
            });
          });
      });
  },
};
</script>

<style scoped lang="scss">
.container-list {
  // 样式区域
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
</style>
