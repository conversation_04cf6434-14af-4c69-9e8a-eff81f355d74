<template>
  <ly-default title="使用须知" title-position="left">
    <view class="common-com common-pd container">
      <view class="container-box">
        <view class="container-title"> 有效期 </view>
        <view class="container-content"> {{ eventData?.renderTime }}</view>

        <view class="container-title"> 试用人数 </view>
        <view class="container-content"> 每张消费券最多一人使用 </view>

        <view class="container-title"> 使用方式 </view>
        <view class="container-content">
          商家扫码核验券码，核验成功后，用户在支付时可直接抵扣相应的券额，按实际支付金额结算
        </view>
      </view>
    </view>
  </ly-default>
</template>

<script setup lang="ts">
import { usePathData } from "@/hook/usePathData";

const { eventData } = usePathData("pageData");
</script>

<style scoped lang="scss">
.container {
  // 样式区域
  padding-bottom: 24rpx;
  padding-top: 24rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  &-box {
    white-space: pre-wrap;
    border-radius: 16rpx;
    background: #fff;
    flex: 1;
    padding: 24rpx;
  }
  &-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    line-height: 32rpx;
    margin-bottom: 20rpx;
  }
  &-content {
    font-size: 28rpx;
    font-weight: 400;
    color: #666666;
    line-height: 40rpx;
    margin-bottom: 40rpx;
  }
}
</style>
