<template>
  <ly-default title="关于我们">
    <view class="about">
      <nut-cell-group>
        <nut-cell @click="goUserAgreement" title="用户协议" is-link />
        <nut-cell @click="goPrivacyPolicy" title="隐私政策" is-link />
      </nut-cell-group>
    </view>
  </ly-default>
</template>

<script setup lang="ts">
import { goUserAgreement, goPrivacyPolicy, back } from "@/router/topage";
</script>

<style scoped lang="scss">
.about {
  padding: 0 24rpx;
  box-sizing: border-box;
}
</style>
