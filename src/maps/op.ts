import { createMap } from "@/utils/tools";
import type { CreateMapParams } from "#/tools";

export const OrderTypeList: CreateMapParams[] = [
  [0, "门诊", "#FF851D"],
  [1, "住院", "#1F90FF"],
];
export const OrderTypeArray = createMap(OrderTypeList);

// 状态;stage=暂存 init=待审核，approve=同意，refuse=拒绝
export const AuditStatusList: CreateMapParams[] = [
  [
    "stage",
    "暂存",
    "#FFB000",
    {
      show: false,
    },
  ],
  [
    "init",
    "待审核",
    "#1E90FF",
    {
      show: false,
    },
  ],
  [
    "approve",
    "审核通过",
    "#26C7B7",
    {
      show: true,
    },
  ],
  [
    "refuse",
    "审核拒绝",
    "#FF0000",
    {
      show: true,
    },
  ],
];

export const AuditStatusArray = createMap(AuditStatusList);

export const EducationList: CreateMapParams[] = [
  [1, "大专", "#FFB000"],
  [2, "本科", "#26C7B7"],
  [3, "硕士", "#A57AFA"],
  [4, "博士", "#FF5252"],
];

export const EducationArray = createMap(EducationList);

// 性别
export const SexList: CreateMapParams[] = [
  [1, "男", "#1E90FF"],
  [2, "女", "#FF5252"],
];

export const SexArray = createMap(SexList);
