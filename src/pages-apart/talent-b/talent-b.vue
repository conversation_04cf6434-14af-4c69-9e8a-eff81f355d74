<template>
  <ly-default title="申请人才公寓" title-position="left">
    <view class="common-com common-pd">
      <view>
        <nut-form ref="formBasic" :model-value="state">
          <view class="custom-form-card-title">
            <zk-text size="32" color="#333">企业基本信息</zk-text>
          </view>
          <nut-form-item
            prop="enterpriseName"
            required
            label="企业名称"
            label-align="left"
            :rules="[
              {
                required: true,
                message: '请输入',
              },
            ]"
          >
            <nut-input
              placeholderClass="custom-input-placeholder"
              placeholder="请输入"
              inputClass="custom-input"
              v-model:model-value="state.enterpriseName"
            ></nut-input>
          </nut-form-item>
          <nut-form-item
            prop="enterpriseAddress"
            required
            label="办公地址"
            label-align="left"
            :rules="[
              {
                required: true,
                message: '请输入',
              },
            ]"
          >
            <nut-input
              placeholderClass="custom-input-placeholder"
              placeholder="请输入"
              inputClass="custom-input"
              v-model:model-value="state.enterpriseAddress"
            ></nut-input>
          </nut-form-item>
          <nut-form-item
            prop="enterpriseContact"
            required
            label="企业联系人"
            label-align="left"
            :rules="[
              {
                required: true,
                message: '请输入',
              },
            ]"
          >
            <nut-input
              placeholderClass="custom-input-placeholder"
              placeholder="请输入"
              inputClass="custom-input"
              v-model:model-value="state.enterpriseContact"
            ></nut-input>
          </nut-form-item>
          <nut-form-item
            prop="enterprisePhone"
            required
            label="联系方式"
            label-align="left"
            :rules="[
              {
                required: true,
                message: '请输入',
              },
            ]"
          >
            <nut-input
              inputClass="custom-input"
              placeholderClass="custom-input-placeholder"
              placeholder="请输入"
              v-model:model-value="state.enterprisePhone"
            ></nut-input>
          </nut-form-item>
          <nut-form-item
            prop="enterpriseType"
            required
            label="企业类型"
            label-align="left"
            :rules="[
              {
                required: true,
                message: '请选择',
              },
            ]"
          >
            <zk-picker
              v-model:value="state.enterpriseType"
              :options="enterpriseTypeOptions"
              placeholder="请选择"
            ></zk-picker>
          </nut-form-item>
          <nut-form-item
            v-if="state.enterpriseType === 5"
            prop="enterpriseOtherType"
            required
            label="其他类型"
            label-align="left"
            :rules="[
              {
                required: true,
                message: '请输入',
              },
            ]"
          >
            <nut-input
              inputClass="custom-input"
              placeholderClass="custom-input-placeholder"
              placeholder="请输入"
              v-model:model-value="state.enterpriseOtherType"
            ></nut-input>
          </nut-form-item>
        </nut-form>
        <nut-form ref="form1" :rules="[]" :model-value="state">
          <nut-form-item
            required
            label-position="top"
            label-align="left"
            prop="businessLicense"
            :rules="[
              {
                required: true,
                message: '请上传',
              },
            ]"
          >
            <template #label>
              <zk-text size="32" color="#333">企业营业执照</zk-text>
            </template>
            <zk-upload-file
              v-model:value="state.businessLicense"
              title="上传附件"
            ></zk-upload-file>
          </nut-form-item>
        </nut-form>
        <nut-form ref="form2" :rules="[]" :model-value="state">
          <nut-form-item
            required
            label-position="top"
            label-align="left"
            prop="enterpriseTypeAttach"
            :rules="[
              {
                required: true,
                message: '请上传',
              },
            ]"
          >
            <template #label>
              <zk-text size="32" color="#333">企业类型证明</zk-text>
            </template>
            <zk-upload-file
              v-model:value="state.enterpriseTypeAttach"
              title="上传附件"
            ></zk-upload-file>
          </nut-form-item>
        </nut-form>
      </view>
    </view>

    <template #bottom>
      <view class="common-pd">
        <nut-button :loading="loading" type="primary" block @click="methods.ok"
          >提交</nut-button
        >
      </view>
    </template>
  </ly-default>
</template>

<script setup lang="ts">
import { usePathData } from "@/hook/usePathData";
import { enterpriseTypeOptions } from "./optData";
import { get } from "lodash-es";
import { apiApplyApartment } from "@/api/apart";
import { useApiLoading } from "@/hook/useApiLoading";
import { goToPage } from "@/router/topage";
const { eventData } = usePathData("pageData");
const formBasic = ref();
const form1 = ref();
const form2 = ref();
const { loading, reload } = useApiLoading({
  api: apiApplyApartment,
  immediate: false,
  toastLoading: true,
});
const state = ref({
  enterpriseName: "",
  enterpriseAddress: "",
  enterpriseContact: "",
  enterprisePhone: "",
  enterpriseType: "",
  enterpriseOtherType: "",
  businessLicense: "",
  enterpriseTypeAttach: "",
});

const methods = {
  async ok() {
    // 开始校验
    // 先从身份证校验

    const { validate: basicValidate } = await formBasic.value!;
    const { validate: form1Validate } = await form1.value!;
    const { validate: form2Validate } = await form2.value!;

    // 同时校验两个
    const [basicV, form1V, form2V] = await Promise.all([
      basicValidate(),
      form1Validate(),
      form2Validate(),
    ]);
    if (basicV.valid && form1V.valid && form2V.valid) {
      console.log("校验通过");
      console.log(state.value);
      // 开始拼参数
      const params = {
        bapartmentDTO: unref(state),
        talentType: get(unref(eventData), "talentType"),
        applyType: 1,
        targetId: get(unref(eventData), "targetId"),
        subTargetId: get(unref(eventData), "subTargetId"),
      };
      reload(params).then(() => {
        goToPage("/pages-apart/talent-result/talent-result", {
          status: "success",
        });
      });
    }
  },
};
</script>

<style scoped lang="scss">
.container {
  // 样式区域
  &-id {
    display: flex;
    gap: 10rpx;
    &-item {
      display: inline-block;
    }
  }
}
</style>
