<template>
  <ly-default title="申请人才公寓" title-position="left">
    <view class="common-com common-pd container">
      <view class="container-card">
        <view class="container-card-empty">
          <zk-empty
            :status="status"
            :title="showstatus"
            :des="showdes"
          ></zk-empty>
        </view>

        <view class="container-card-action">
          <view class="button">
            <nut-button block type="primary" @click="goHome()">
              返回首页
            </nut-button>
          </view>
          <view class="button">
            <nut-button block @click="goToPage('/pages-apart/record/record')">
              查看申请记录
            </nut-button>
          </view>
        </view>
      </view>
    </view>
  </ly-default>
</template>

<script setup lang="ts">
import { goHome, goToPage } from "@/router/topage";
import { usePathData } from "@/hook/usePathData";
import { find, get } from "lodash-es";
const { params } = usePathData();

const status = computed(() => {
  return get(params.value, "success", "success");
});

const options = [
  {
    status: "success",
    title: "提交成功",
    des: "您的申请已提交成功，请耐心等待审核结果",
  },
  {
    status: "fail",
    title: "提交失败",
    des: "您的申请提交失败，请重新提交",
  },
];

const showstatus = computed(() => {
  return find(options, { status: status.value })?.title;
});

const showdes = computed(() => {
  return find(options, { status: status.value })?.des;
});
</script>

<style scoped lang="scss">
.container {
  // 样式区域
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  &-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    margin: 50rpx 24rpx 24rpx 24rpx;
    background: #fff;
    padding: 0 24rpx;
    border-radius: 16rpx;
    &-empty {
      margin-top: 184rpx;
    }
    &-action {
      margin-top: 80rpx;
      display: flex;
      gap: 24rpx;
      & .button {
        flex: 1;
        flex-shrink: 0;
      }
    }
  }
}
</style>
