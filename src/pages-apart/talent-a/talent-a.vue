<template>
  <ly-default title="申请人才公寓" title-position="left">
    <view class="common-com common-pd">
      <view>
        <nut-form ref="formId" :model-value="state">
          <nut-form-item
            prop="idCardFrontAttach"
            required
            label-align="left"
            label-position="top"
            :rules="[{ required: true, message: '请上传' }]"
          >
            <template #label>
              <zk-text size="32" color="#333">身份证上传</zk-text>
            </template>
            <view class="container-id">
              <view class="container-id-item">
                <zk-upload-image
                  v-model:value="state.idCardFrontAttach"
                  title="身份证正面"
                ></zk-upload-image>
              </view>
              <view class="container-id-item">
                <zk-upload-image
                  v-model:value="state.idCardBackAttach"
                  title="身份证反面"
                ></zk-upload-image>
              </view>
            </view>
          </nut-form-item>
        </nut-form>
        <nut-form ref="formBasic" :model-value="state">
          <view class="custom-form-card-title">
            <zk-text size="32" color="#333">基本信息</zk-text>
          </view>
          <nut-form-item
            label-position="top"
            label="照片"
            label-align="left"
            prop="photo"
          >
            <zk-upload-image
              v-model:value="state.photo"
              title="上传2寸免冠照片"
            ></zk-upload-image>
          </nut-form-item>
          <nut-form-item
            :rules="[{ required: true, message: '请输入' }]"
            prop="name"
            required
            label="姓名"
            label-align="left"
          >
            <nut-input
              placeholderClass="custom-input-placeholder"
              placeholder="请输入姓名"
              inputClass="custom-input"
              v-model:model-value="state.name"
            ></nut-input>
          </nut-form-item>
          <nut-form-item
            :rules="[{ required: true, message: '请选择' }]"
            prop="sex"
            required
            label="性别"
            label-align="left"
          >
            <view class="custom-right">
              <nut-radio-group
                direction="horizontal"
                placeholderClass="custom-input-placeholder"
                placeholder="性别"
                v-model:model-value="state.sex"
              >
                <nut-radio label="1">男</nut-radio>
                <nut-radio label="2">女</nut-radio>
              </nut-radio-group>
            </view>
          </nut-form-item>
          <nut-form-item
            prop="nativePlace"
            required
            label="籍贯"
            label-align="left"
            :rules="[{ required: true, message: '请输入' }]"
          >
            <nut-input
              inputClass="custom-input"
              placeholderClass="custom-input-placeholder"
              placeholder="请输入"
              v-model:model-value="state.nativePlace"
            ></nut-input>
          </nut-form-item>
          <nut-form-item
            prop="birthday"
            required
            label="出生年月"
            label-align="left"
            :rules="[{ required: true, message: '请选择' }]"
          >
            <zk-date-picker
              placeholder="选择日期"
              type="date"
              value-format="YYYY-MM-DD"
              v-model:value="state.birthday"
            ></zk-date-picker>
          </nut-form-item>
          <nut-form-item
            prop="idCard"
            required
            label="身份证号"
            label-align="left"
            :rules="[{ required: true, message: '请输入' }]"
          >
            <nut-input
              inputClass="custom-input"
              placeholderClass="custom-input-placeholder"
              placeholder="请输入"
              v-model:model-value="state.idCard"
            ></nut-input>
          </nut-form-item>
          <nut-form-item
            prop="memberAddress"
            required
            label="出生地"
            label-align="left"
            :rules="[{ required: true, message: '请输入' }]"
          >
            <nut-input
              inputClass="custom-input"
              placeholderClass="custom-input-placeholder"
              placeholder="请输入"
              v-model:model-value="state.memberAddress"
            ></nut-input>
          </nut-form-item>
          <nut-form-item
            :rules="[{ required: true, message: '请输入' }]"
            prop="nation"
            required
            label="民族"
            label-align="left"
          >
            <nut-input
              inputClass="custom-input"
              placeholderClass="custom-input-placeholder"
              placeholder="请输入"
              v-model:model-value="state.nation"
            ></nut-input>
          </nut-form-item>
          <nut-form-item
            prop="memberPhone"
            required
            label="本人电话"
            label-align="left"
            :rules="[{ required: true, message: '请输入' }]"
          >
            <nut-input
              inputClass="custom-input"
              placeholderClass="custom-input-placeholder"
              placeholder="请输入"
              v-model:model-value="state.memberPhone"
            ></nut-input>
          </nut-form-item>

          <nut-form-item
            prop="maritalStatus"
            required
            label="婚姻状态"
            label-align="left"
            :rules="[{ required: true, message: '请选择' }]"
          >
            <zk-picker
              v-model:value="state.maritalStatus"
              :options="optionMaritalStatus"
              placeholder="请选择"
            ></zk-picker>
          </nut-form-item>
          <template v-if="state.maritalStatus === 2">
            <nut-form-item label="配偶姓名" label-align="left">
              <nut-input
                inputClass="custom-input"
                placeholderClass="custom-input-placeholder"
                placeholder="请输入"
                v-model:model-value="state.spouseName"
              ></nut-input>
            </nut-form-item>
            <nut-form-item label="配偶身份证号" label-align="left">
              <nut-input
                inputClass="custom-input"
                placeholderClass="custom-input-placeholder"
                placeholder="请输入"
                v-model:model-value="state.spouseIdCard"
              ></nut-input>
            </nut-form-item>
          </template>
        </nut-form>
      </view>
    </view>

    <template #bottom>
      <view class="common-pd">
        <nut-button :loading="loading" type="primary" block @click="methods.ok"
          >提交</nut-button
        >
      </view>
    </template>
  </ly-default>
</template>

<script setup lang="ts">
import { usePathData } from "@/hook/usePathData";
import { optionMaritalStatus } from "@/pages-apart/talent-a/optData";
import { get } from "lodash-es";
import { apiApplyApartment } from "@/api/apart";
import { useApiLoading } from "@/hook/useApiLoading";
import { goToPage } from "@/router/topage";
const { eventData } = usePathData("pageData");
const formBasic = ref();
const formId = ref();
const { loading, reload } = useApiLoading({
  api: apiApplyApartment,
  immediate: false,
  toastLoading: true,
});
const state = ref({
  idCardFrontAttach: "",
  idCardBackAttach: "",
  name: "",
  sex: "",
  nativePlace: "",
  birthday: "",
  idCard: "",
  memberAddress: "",
  nation: "",
  memberPhone: "",
  maritalStatus: "",
  spouseName: "",
  spouseIdCard: "",
  photo: "",
});

const methods = {
  async ok() {
    // 开始校验
    // 先从身份证校验
    const { validate } = await formId.value!;
    const { validate: basicValidate } = await formBasic.value!;
    // 同时校验两个
    const [idCardValidate, basicValidateResult] = await Promise.all([
      validate(),
      basicValidate(),
    ]);
    if (idCardValidate.valid && basicValidateResult.valid) {
      console.log("校验通过");
      console.log(state.value);
      // 开始拼参数
      const params = {
        aapartmentDTO: unref(state),
        talentType: get(unref(eventData), "talentType"),
        applyType: 1,
        targetId: get(unref(eventData), "targetId"),
        subTargetId: get(unref(eventData), "subTargetId"),
      };
      reload(params).then(() => {
        goToPage("/pages-apart/talent-result/talent-result", {
          status: "success",
        });
      });
    } else {
      uni.showToast({
        title: "请完善表单",
        icon: "none",
      });
    }
  },
};
</script>

<style scoped lang="scss">
.container {
  // 样式区域
  &-id {
    display: flex;
    gap: 10rpx;
    &-item {
      display: inline-block;
    }
  }
}
</style>
