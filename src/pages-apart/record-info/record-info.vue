<template>
  <ly-info title="详细" title-position="left">
    <view class="common-com common-pd container">
      <view class="container-top">
        <RecordItem :item="eventData"></RecordItem>
      </view>
      <view class="container-bottom">
        <zk-card height="100%" title="审核详情">
          <view>
            <zk-steps-one
              :current="stepsStatus.current"
              :status="stepsStatus.status"
              :options="stepsStatus.options"
            ></zk-steps-one>
          </view>
        </zk-card>
      </view>
    </view>
  </ly-info>
</template>

<script setup lang="ts">
import { usePathData } from "@/hook/usePathData";
import RecordItem from "./record-item.vue";
import type { ZkStepsOneSchema } from "#/component";
import { get } from "lodash-es";
const { eventData } = usePathData("pageData");

interface StepsStatus {
  status: "success" | "error" | "wait";
  options: ZkStepsOneSchema[];
  current: number | string;
}

const stepsStatus = computed<StepsStatus>(() => {
  // 获取状态
  const state = get(unref(eventData), "state");
  if (!state) {
    return {
      current: "1",
      status: "error",
      options: [
        {
          label: "待提交",
          value: "1",
        },
        {
          label: "审核中",
          value: "2",
        },
      ],
      des: "获取状态失败",
    };
  }
  if (state === "stage") {
    return {
      current: "1",
      status: "wait",
      options: [
        {
          label: "待提交",
          value: "1",
        },
        {
          label: "审核中",
          value: "2",
        },
      ],
    };
  } else if (state === "init") {
    return {
      current: "1",
      status: "success",
      options: [
        {
          label: "已提交",
          value: "1",
          des: `申请人:${get(unref(eventData), "submitBy")}`,
        },
        {
          label: "待审核",
          value: "2",
        },
      ],
    };
  } else if (state === "approve") {
    return {
      status: "success",
      current: "2",
      options: [
        {
          label: "已提交",
          value: "1",
          des: `申请人:${get(unref(eventData), "submitBy")}`,
        },
        {
          label: "已受理",
          value: "2",
          des: `审核意见:${get(unref(eventData), "reasons") || "无"}`,
        },
      ],
    };
  } else if (state === "refuse") {
    return {
      current: "2",
      status: "error",
      options: [
        {
          label: "已提交",
          value: "1",
          des: `申请人:${get(unref(eventData), "submitBy")}`,
        },
        {
          label: "已受理",
          value: "2",
          des: `审核意见:${get(unref(eventData), "reasons") || "无"}`,
        },
      ],
    };
  }

  return {
    current: "1",
    status: "error",
    options: [],
  };
});
</script>

<style scoped lang="scss">
.container {
  // 样式区域
  gap: 20rpx;

  &-top {
    margin-top: 50rpx;
  }
  &-bottom {
    flex: 1;
    margin-bottom: 20rpx;
  }
}
</style>
