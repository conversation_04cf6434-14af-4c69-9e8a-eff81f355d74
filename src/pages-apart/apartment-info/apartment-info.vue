<template>
  <ly-default title="公寓详情" transparent="#fff8" title-position="left">
    <view class="common-com common-pd container">
      <view class="container-top">
        <zk-card-info>
          <template #title>
            <view>
              <view>
                <zk-text size="32" bold color="#1D2129">
                  <text>
                    {{ get(eventData, "name", "--") }}
                  </text>
                </zk-text>
              </view>
              <view>
                <zk-text size="24" color="#999">
                  <text>
                    {{ get(eventData, "address", "--") }}
                  </text>
                </zk-text>
              </view>
            </view>
          </template>
          <view>
            <view class="container-se">
              <zk-text bold size="28" color="#333">选择房型</zk-text>
            </view>
            <zk-radio-three
              v-model:value="subTargetId"
              :options="modelList"
            ></zk-radio-three>
          </view>
        </zk-card-info>
      </view>
      <view>
        <zk-card-info title="房型配置">
          <view class="container-config">
            <view
              class="container-config-tag"
              v-for="(item, index) in get(eventData, 'typeConfigList', [])"
              :key="index"
            >
              {{ item }}
            </view>
          </view>
        </zk-card-info>
      </view>
      <view>
        <zk-card-info title="房源介绍">
          <view>
            <rich-text :nodes="introduction"></rich-text>
          </view>
        </zk-card-info>
      </view>
    </view>
    <image
      mode="aspectFill"
      class="container-bg"
      :src="get(eventData, 'thumbnail')"
    ></image>
    <template #bottom>
      <view class="container-bottom">
        <nut-button
          size="large"
          @click="methods.openTips()"
          type="primary"
          block
          >申请公寓</nut-button
        >
      </view>
    </template>
    <zk-affirm
      @on-ok="methods.ok()"
      title="申请须知"
      :open="openTips"
      :text="describe"
      control
      @on-cancel="methods.cancel"
    ></zk-affirm>
  </ly-default>
</template>

<script setup lang="ts">
import { usePathData } from "@/hook/usePathData";
import { first, get, map, size } from "lodash-es";
// import { affirmData } from "./affirmData";
import { goToPage } from "@/router/topage";
const { eventData } = usePathData("pageData", () => {
  if (size(unref(modelList))) {
    subTargetId.value = first(unref(modelList))?.value;
  }
});
const describe = computed(() => {
  const _describe = get(unref(eventData), "describe");
  return _describe || "暂无介绍";
});
const introduction = computed(() => {
  const _introduction = get(unref(eventData), "introduction") || "";
  return _introduction.replace(/<(\/)?pre[^>]*>/gi, "") || "暂无介绍";
});
// 房型列表
const modelList = computed(() => {
  if (unref(eventData)) {
    const l = get(unref(eventData), "modelList") || [];
    return map(l, (item) => {
      return {
        label: get(item, "name"),
        value: get(item, "id"),
      };
    });
  }
  return [];
});

const subTargetId = ref("");

const openTips = ref(false);
const methods = {
  openTips() {
    openTips.value = true;
  },
  ok() {
    openTips.value = false;
    console.log(get(eventData.value, "applyType"));
    if (get(eventData.value, "applyType") === "1") {
      goToPage("/pages-apart/select-type/select-type", {
        subTargetId: unref(subTargetId),
        applyType: "1",
        targetId: get(unref(eventData), "id"),
      });
    } else if (get(eventData.value, "applyType") === "2") {
      goToPage("/pages-apart/talent-ent/talent-ent", {
        subTargetId: unref(subTargetId),
        applyType: "2",
        targetId: get(unref(eventData), "id"),
      });
    }
  },
  cancel() {
    openTips.value = false;
  },
};
</script>

<style scoped lang="scss">
.container {
  gap: 24rpx;
  &-config {
    display: flex;
    flex-wrap: wrap;
    gap: 24rpx;
    &-tag {
      padding: 12rpx 24rpx;
      border-radius: 8rpx;
      background-color: #f2f3f5;
      color: #999;
    }
  }
  &-top {
    margin-top: 300rpx;
  }
  // 样式区域
  &-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 577rpx;
    z-index: -1;
  }
  &-se {
    margin-top: 20rpx;
    margin-bottom: 20rpx;
  }
  &-bottom {
    padding: 24rpx 24rpx 0 24rpx;
  }
}
</style>
<style>
[alt] {
  max-width: 100%;
  height: auto;
}
</style>
