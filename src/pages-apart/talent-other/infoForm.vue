<script setup lang="ts">
import { useRuleFormItem } from "@/hook/useFormItem";
interface Props {
  value: any;
}
const props = defineProps<Props>();
const emit = defineEmits(["update:value"]);
const [state] = useRuleFormItem<Props, keyof Props, any>(props, "value", [
  "update:value",
]);

const formRef = ref();
const submit = async () => {
  const { validate } = await formRef.value!;
  // 同时校验两个
  const [basicV] = await Promise.all([validate()]);
  if (basicV.valid) {
    return Promise.resolve(unref(state));
  }
  return Promise.reject();
};

defineExpose({
  submit,
});
</script>

<template>
  <view>
    <nut-form ref="formRef" :model-value="state">
      <view class="custom-form-card-title">
        <zk-text size="32" color="#333">申请信息</zk-text>
      </view>

      <nut-form-item
        prop="applyTerm"
        required
        label="申请租期"
        label-width="250rpx"
        label-align="left"
        :rules="[{ required: true, message: '请选择' }]"
      >
        <zk-date-picker
          placeholder="选择日期"
          type="date"
          value-format="YYYY-MM-DD"
          v-model:value="state.applyTerm"
        ></zk-date-picker>
      </nut-form-item>
      <nut-form-item
        prop="applyReason"
        required
        label="申请理由"
        label-align="left"
        :rules="[{ required: true, message: '请输入' }]"
      >
        <nut-textarea
          inputClass="custom-input"
          placeholderClass="custom-input-placeholder"
          placeholder="请输入"
          v-model:model-value="state.applyReason"
        ></nut-textarea>
      </nut-form-item>
    </nut-form>
  </view>
</template>

<style scoped lang="scss">
.container {
  // 样式区域
  &-id {
    display: flex;
    gap: 10rpx;
    &-item {
      display: inline-block;
    }
  }
}
</style>
