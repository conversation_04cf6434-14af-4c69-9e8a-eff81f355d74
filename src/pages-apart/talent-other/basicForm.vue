<script setup lang="ts">
import { optionMaritalStatus } from "@/pages-apart/talent-a/optData";
import { useRuleFormItem } from "@/hook/useFormItem";
interface Props {
  value: any;
}
const props = defineProps<Props>();
const emit = defineEmits(["update:value"]);
const [state] = useRuleFormItem<Props, keyof Props, any>(props, "value", [
  "update:value",
]);

const formRef = ref();
const formRef2 = ref();
const submit = async () => {
  const { validate } = await formRef.value!;
  const { validate: validate2 } = await formRef2.value!;
  // 同时校验两个
  const [basicV, form1V] = await Promise.all([validate(), validate2()]);
  if (basicV.valid && form1V.valid) {
    // 这里表单验证无法验证身份证
    if (unref(state)?.idCardFrontAttach && unref(state)?.idCardBackAttach) {
      return Promise.resolve(unref(state));
    } else {
      uni.showToast({
        title: "请上传身份证",
        icon: "none",
      });
      return Promise.reject();
    }
  }
  return Promise.reject();
};
// :rules="[
// {
//   required: true,
//       message: '请上传照片',
// },
// ]"
defineExpose({
  submit,
});
</script>

<template>
  <view>
    <nut-form ref="formRef" :model-value="state">
      <nut-form-item
        prop="idCardFrontAttach"
        required
        label-align="left"
        label-position="top"
        :rules="[{ required: true, message: '请上传' }]"
      >
        <template #label>
          <zk-text size="32" color="#333">身份证上传</zk-text>
        </template>
        <view class="container-id">
          <view class="container-id-item">
            <zk-upload-image
              v-model:value="state.idCardFrontAttach"
              title="身份证正面"
            ></zk-upload-image>
          </view>
          <view class="container-id-item">
            <zk-upload-image
              v-model:value="state.idCardBackAttach"
              title="身份证反面"
            ></zk-upload-image>
          </view>
        </view>
      </nut-form-item>
    </nut-form>
    <nut-form ref="formRef2" :model-value="state">
      <view class="custom-form-card-title">
        <zk-text size="32" color="#333">基本信息</zk-text>
      </view>
      <nut-form-item
        label-position="top"
        label="照片"
        label-align="left"
        prop="photo"
      >
        <zk-upload-image
          v-model:value="state.photo"
          title="上传2寸免冠照片"
        ></zk-upload-image>
      </nut-form-item>
      <nut-form-item
        :rules="[{ required: true, message: '请输入' }]"
        prop="name"
        required
        label="姓名"
        label-align="left"
      >
        <nut-input
          placeholderClass="custom-input-placeholder"
          placeholder="请输入姓名"
          inputClass="custom-input"
          v-model:model-value="state.name"
        ></nut-input>
      </nut-form-item>
      <nut-form-item
        :rules="[{ required: true, message: '请选择' }]"
        prop="sex"
        required
        label="性别"
        label-align="left"
      >
        <view class="custom-right">
          <nut-radio-group
            direction="horizontal"
            placeholderClass="custom-input-placeholder"
            placeholder="性别"
            v-model:model-value="state.sex"
          >
            <nut-radio label="1">男</nut-radio>
            <nut-radio label="2">女</nut-radio>
          </nut-radio-group>
        </view>
      </nut-form-item>
      <nut-form-item
        prop="nativePlace"
        required
        label="籍贯"
        label-align="left"
        :rules="[{ required: true, message: '请输入' }]"
      >
        <nut-input
          inputClass="custom-input"
          placeholderClass="custom-input-placeholder"
          placeholder="请输入"
          v-model:model-value="state.nativePlace"
        ></nut-input>
      </nut-form-item>
      <nut-form-item
        prop="birthday"
        required
        label="出生年月"
        label-align="left"
        :rules="[{ required: true, message: '请选择' }]"
      >
        <zk-date-picker
          placeholder="选择日期"
          type="date"
          v-model:value="state.birthday"
          value-format="YYYY-MM-DD"
        ></zk-date-picker>
      </nut-form-item>
      <nut-form-item
        :rules="[{ required: true, message: '请输入' }]"
        prop="idCard"
        required
        label="身份证号"
        label-align="left"
      >
        <nut-input
          inputClass="custom-input"
          placeholderClass="custom-input-placeholder"
          placeholder="请输入"
          v-model:model-value="state.idCard"
        ></nut-input>
      </nut-form-item>
      <nut-form-item
        prop="memberAddress"
        required
        label="出生地"
        label-align="left"
        :rules="[{ required: true, message: '请输入' }]"
      >
        <nut-input
          inputClass="custom-input"
          placeholderClass="custom-input-placeholder"
          placeholder="请输入"
          v-model:model-value="state.memberAddress"
        ></nut-input>
      </nut-form-item>
      <nut-form-item
        :rules="[{ required: true, message: '请输入' }]"
        prop="nation"
        required
        label="民族"
        label-align="left"
      >
        <nut-input
          inputClass="custom-input"
          placeholderClass="custom-input-placeholder"
          placeholder="请输入"
          v-model:model-value="state.nation"
        ></nut-input>
      </nut-form-item>
      <nut-form-item
        prop="memberPhone"
        required
        label="本人电话"
        label-align="left"
        :rules="[{ required: true, message: '请输入' }]"
      >
        <nut-input
          inputClass="custom-input"
          placeholderClass="custom-input-placeholder"
          placeholder="请输入"
          v-model:model-value="state.memberPhone"
        ></nut-input>
      </nut-form-item>

      <nut-form-item
        prop="maritalStatus"
        required
        label="婚姻状态"
        label-align="left"
        :rules="[{ required: true, message: '请选择' }]"
      >
        <zk-picker
          v-model:value="state.maritalStatus"
          :options="optionMaritalStatus"
          placeholder="请选择"
        ></zk-picker>
      </nut-form-item>
      <template v-if="state.maritalStatus === 2">
        <nut-form-item label="配偶姓名" label-align="left">
          <nut-input
            inputClass="custom-input"
            placeholderClass="custom-input-placeholder"
            placeholder="请输入"
            v-model:model-value="state.spouseName"
          ></nut-input>
        </nut-form-item>
        <nut-form-item label="配偶身份证号" label-align="left">
          <nut-input
            inputClass="custom-input"
            placeholderClass="custom-input-placeholder"
            placeholder="请输入"
            v-model:model-value="state.spouseIdCard"
          ></nut-input>
        </nut-form-item>
      </template>
    </nut-form>
  </view>
</template>

<style scoped lang="scss">
.container {
  // 样式区域
  &-id {
    display: flex;
    gap: 10rpx;
    &-item {
      display: inline-block;
    }
  }
}
</style>
