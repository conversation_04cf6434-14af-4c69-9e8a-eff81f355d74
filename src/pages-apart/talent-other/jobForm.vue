<script setup lang="ts">
import { useRuleFormItem } from "@/hook/useFormItem";
interface Props {
  value: any;
}
const props = defineProps<Props>();
const emit = defineEmits(["update:value"]);
const [state] = useRuleFormItem<Props, keyof Props, any>(props, "value", [
  "update:value",
]);

const formRef = ref();
const formRef2 = ref();
const submit = async () => {
  const { validate } = await formRef.value!;
  const { validate: validate2 } = await formRef2.value!;
  // 同时校验两个
  const [basicV, form1V] = await Promise.all([validate(), validate2()]);
  if (basicV.valid && form1V.valid) {
    return Promise.resolve(unref(state));
  }
  return Promise.reject();
};

defineExpose({
  submit,
});
</script>

<template>
  <view>
    <nut-form ref="formRef" :model-value="state">
      <view class="custom-form-card-title">
        <zk-text size="32" color="#333">单位信息</zk-text>
      </view>
      <nut-form-item
        prop="entryDate"
        required
        label="参加工作时间"
        label-align="left"
        :rules="[{ required: true, message: '请选择' }]"
      >
        <zk-date-picker
          placeholder="选择日期"
          type="date"
          value-format="YYYY-MM-DD"
          v-model:value="state.entryDate"
        ></zk-date-picker>
      </nut-form-item>
      <nut-form-item
        prop="technicalTitle"
        required
        label="专业技术职称"
        label-align="left"
        :rules="[{ required: true, message: '请输入' }]"
      >
        <nut-input
          inputClass="custom-input"
          placeholderClass="custom-input-placeholder"
          placeholder="请输入"
          v-model:model-value="state.technicalTitle"
        ></nut-input>
      </nut-form-item>
      <nut-form-item
        prop="enterpriseName"
        required
        label="现工作单位"
        label-align="left"
        :rules="[{ required: true, message: '请输入' }]"
      >
        <nut-input
          inputClass="custom-input"
          placeholderClass="custom-input-placeholder"
          placeholder="请输入"
          v-model:model-value="state.enterpriseName"
        ></nut-input>
      </nut-form-item>

      <nut-form-item
        prop="enterpriseContact"
        required
        label="单位联系人"
        label-align="left"
        :rules="[{ required: true, message: '请输入' }]"
      >
        <nut-input
          inputClass="custom-input"
          placeholderClass="custom-input-placeholder"
          placeholder="请输入"
          v-model:model-value="state.enterpriseContact"
        ></nut-input>
      </nut-form-item>
      <nut-form-item
        prop="enterprisePhone"
        required
        label="单位联系电话"
        label-align="left"
        :rules="[{ required: true, message: '请输入' }]"
      >
        <nut-input
          inputClass="custom-input"
          placeholderClass="custom-input-placeholder"
          placeholder="请输入"
          v-model:model-value="state.enterprisePhone"
        ></nut-input>
      </nut-form-item>
      <nut-form-item
        :rules="[{ required: true, message: '请输入' }]"
        prop="postType"
        required
        label="职务"
        label-align="left"
      >
        <nut-input
          inputClass="custom-input"
          placeholderClass="custom-input-placeholder"
          placeholder="请输入"
          v-model:model-value="state.postType"
        ></nut-input>
      </nut-form-item>
      <nut-form-item
        prop="laborContractTermStart"
        required
        label="劳动合同签开始"
        label-width="250rpx"
        label-align="left"
        :rules="[{ required: true, message: '请选择' }]"
      >
        <zk-date-picker
          placeholder="选择日期"
          type="date"
          value-format="YYYY-MM-DD"
          v-model:value="state.laborContractTermStart"
        ></zk-date-picker>
      </nut-form-item>
      <nut-form-item
        prop="laborContractTermEnd"
        required
        label="劳动合同签截止"
        label-align="left"
        label-width="250rpx"
        :rules="[{ required: true, message: '请选择' }]"
      >
        <zk-date-picker
          placeholder="选择日期"
          type="date"
          value-format="YYYY-MM-DD"
          v-model:value="state.laborContractTermEnd"
        ></zk-date-picker>
      </nut-form-item>
    </nut-form>
    <nut-form ref="formRef2" :model-value="state">
      <nut-form-item
        required
        label-position="top"
        label-align="left"
        prop="laborContract"
        :rules="[{ required: true, message: '请上传' }]"
      >
        <template #label>
          <zk-text size="32" color="#333">与单位签订的劳动合同</zk-text>
        </template>
        <zk-upload-file
          v-model:value="state.laborContract"
          title="请上传合同附件"
        ></zk-upload-file>
      </nut-form-item>
    </nut-form>
    <nut-form ref="formRef3" :model-value="state">
      <nut-form-item
        required
        label-position="top"
        label-align="left"
        prop="businessLicense"
        :rules="[{ required: true, message: '请上传' }]"
      >
        <template #label>
          <zk-text size="32" color="#333">单位营业执照</zk-text>
        </template>
        <zk-upload-file
          v-model:value="state.businessLicense"
          title="请上传附件"
        ></zk-upload-file>
      </nut-form-item>
    </nut-form>
  </view>
</template>

<style scoped lang="scss">
.container {
  // 样式区域
  &-id {
    display: flex;
    gap: 10rpx;
    &-item {
      display: inline-block;
    }
  }
}
</style>
