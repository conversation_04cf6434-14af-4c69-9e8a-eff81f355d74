<script setup lang="ts">
import { useRuleFormItem } from "@/hook/useFormItem";
interface Props {
  value: any;
}
const props = defineProps<Props>();
const emit = defineEmits(["update:value"]);
const [state] = useRuleFormItem<Props, keyof Props, any>(props, "value", [
  "update:value",
]);

const formRef = ref();
const formRef2 = ref();
const submit = async () => {
  const { validate } = await formRef.value!;
  const { validate: validate2 } = await formRef2.value!;
  // 同时校验两个
  const [basicV, form1V] = await Promise.all([validate(), validate2()]);
  if (basicV.valid && form1V.valid) {
    return Promise.resolve(unref(state));
  }
  return Promise.reject();
};

defineExpose({
  submit,
});
</script>

<template>
  <view>
    <nut-form ref="formRef" :model-value="state">
      <view class="custom-form-card-title">
        <zk-text size="32" color="#333">学历信息</zk-text>
      </view>
      <nut-form-item
        prop="educationType"
        required
        label-align="left"
        label="选择教育类型"
        label-width="200rpx"
        :rules="[{ required: true, message: '请选择' }]"
      >
        <view class="custom-right">
          <nut-radio-group
            direction="horizontal"
            placeholderClass="custom-input-placeholder"
            placeholder=""
            v-model:model-value="state.educationType"
          >
            <nut-radio label="1">全日制教育</nut-radio>
            <nut-radio label="2">在职教育</nut-radio>
          </nut-radio-group>
        </view>
      </nut-form-item>
      <nut-form-item
        prop="admissionDate"
        required
        label="入校时间"
        label-align="left"
        label-width="200rpx"
        :rules="[{ required: true, message: '请选择' }]"
      >
        <zk-date-picker
          placeholder="选择日期"
          value-format="YYYY-MM-DD"
          type="date"
          v-model:value="state.admissionDate"
        ></zk-date-picker>
      </nut-form-item>
      <nut-form-item
        prop="graduationDate"
        required
        label="毕业时间"
        label-align="left"
        label-width="200rpx"
        :rules="[{ required: true, message: '请选择' }]"
      >
        <zk-date-picker
          placeholder="选择日期"
          type="date"
          value-format="YYYY-MM-DD"
          v-model:value="state.graduationDate"
        ></zk-date-picker>
      </nut-form-item>
      <nut-form-item
        :rules="[{ required: true, message: '请输入' }]"
        prop="school"
        required
        label="毕业院校"
        label-align="left"
      >
        <nut-input
          inputClass="custom-input"
          placeholderClass="custom-input-placeholder"
          placeholder="请输入"
          v-model:model-value="state.school"
        ></nut-input>
      </nut-form-item>
      <nut-form-item
        :rules="[{ required: true, message: '请输入' }]"
        prop="major"
        required
        label="所学专业"
        label-align="left"
      >
        <nut-input
          inputClass="custom-input"
          placeholderClass="custom-input-placeholder"
          placeholder="请输入"
          v-model:model-value="state.major"
        ></nut-input>
      </nut-form-item>
    </nut-form>
    <nut-form ref="formRef2" :model-value="state">
      <nut-form-item
        required
        label-position="top"
        label-align="left"
        prop="certificateAttach"
        :rules="[{ required: true, message: '请上传' }]"
      >
        <template #label>
          <zk-text size="32" color="#333">学历证明</zk-text>
        </template>
        <zk-text color="#666">
          请提供学信网的学历证书和学位证书的扫描件，如有多张文件请合并一起上传
        </zk-text>
        <zk-upload-file
          v-model:value="state.certificateAttach"
          title="请上传学信网证书"
        ></zk-upload-file>
      </nut-form-item>
    </nut-form>
  </view>
</template>

<style scoped lang="scss">
.container {
  // 样式区域
  &-id {
    display: flex;
    gap: 10rpx;
    &-item {
      display: inline-block;
    }
  }
}
</style>
