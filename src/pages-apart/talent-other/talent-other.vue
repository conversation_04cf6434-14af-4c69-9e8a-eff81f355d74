<template>
  <ly-default title="申请人才公寓" :safe="false" title-position="left">
    <view class="common-com common-pd">
      <view>
        <zk-steps :current="progress" :options="progressOptions"></zk-steps>
      </view>
      <view>
        <BasicForm
          v-model:value="state"
          ref="basicFormRef"
          v-if="progress === '1'"
        ></BasicForm>
        <!--        <EduForm-->
        <!--          v-model:value="state"-->
        <!--          ref="eduFormRef"-->
        <!--          v-else-if="progress === '2'"-->
        <!--        ></EduForm>-->
        <JobForm
          v-model:value="state"
          ref="jobFormRef"
          v-else-if="progress === '3'"
        ></JobForm>
        <InfoForm
          v-model:value="state"
          ref="infoFormRef"
          v-else-if="progress === '4'"
        ></InfoForm>
      </view>
    </view>

    <template #bottom>
      <view class="container-action">
        <zk-button-action
          :current="progress"
          :options="buttonOption"
          :loading="loading"
        ></zk-button-action>
      </view>
    </template>
    <zk-affirm
      title="承诺书"
      :text="letterApartTipsText"
      :open="affirmOpen"
      control
      @on-ok="methods.submit"
      @on-cancel="affirmOpen = false"
    ></zk-affirm>
  </ly-default>
</template>

<script setup lang="ts">
import { usePathData } from "@/hook/usePathData";
import { progressOptions } from "./optData";
import BasicForm from "./basicForm.vue";
// import EduForm from "./eduForm.vue";
import JobForm from "./jobForm.vue";
import InfoForm from "./infoForm.vue";
import { get } from "lodash-es";
import { apiApplyApartment } from "@/api/apart";
import { useApiLoading } from "@/hook/useApiLoading";
import { back, goToPage } from "@/router/topage";
import { letterApartTipsText } from "@/config/tipsData";
const { eventData } = usePathData("pageData");
const basicFormRef = ref();
// const eduFormRef = ref();
const jobFormRef = ref();
const infoFormRef = ref();
const progress = ref("1");
const affirmOpen = ref(false);
const { loading, reload } = useApiLoading({
  api: apiApplyApartment,
  immediate: false,
  toastLoading: true,
});
const state = ref({
  idCardFrontAttach: "",
  idCardBackAttach: "",
  name: "",
  sex: "1",
  nativePlace: "",
  birthday: "",
  idCard: "",
  nation: "",
  memberPhone: "",
  memberAddress: "",
  maritalStatus: 0,
  spouseName: "",
  spouseIdCard: "",
  educationType: 0,
  educationDate: 0,
  school: "",
  major: "",
  certificateAttach: "",
  entryDate: "",
  technicalTitle: "",
  enterpriseName: "",
  enterpriseContact: "",
  enterprisePhone: "",
  postType: "",
  laborContractTerm: "",
  laborContract: "",
  businessLicense: "",
  applyTerm: "",
  applyReason: "",
});

const methods = {
  async submit() {
    const params = {
      otherApplyApartmentDTO: unref(state),
      talentType: get(unref(eventData), "talentType"),
      applyType: 1,
      targetId: get(unref(eventData), "targetId"),
      subTargetId: get(unref(eventData), "subTargetId"),
    };
    reload(params).then(() => {
      goToPage("/pages-apart/talent-result/talent-result", {
        status: "success",
      });
    });
  },
};

const buttonOption = [
  {
    value: "1",
    cancel: () => {
      back();
    },
    ok: async () => {
      await basicFormRef.value?.submit();
      progress.value = "3";
    },
    // ok按钮扩展
    okExtend: {
      type: "primary",
      text: "下一步",
    },
    cancelExtend: {
      type: "default",
      text: "取消",
    },
  },
  // {
  //   value: "2",
  //   cancel: () => {
  //     progress.value = "1";
  //   },
  //   ok: async () => {
  //     // await eduFormRef.value?.submit();
  //     progress.value = "3";
  //   },
  //   // ok按钮扩展
  //   okExtend: {
  //     type: "primary",
  //     text: "下一步",
  //   },
  //   cancelExtend: {
  //     type: "primary",
  //     text: "上一步",
  //   },
  // },
  {
    value: "3",
    cancel: () => {
      progress.value = "1";
    },
    ok: async () => {
      await jobFormRef.value?.submit();
      progress.value = "4";
    },
    // ok按钮扩展
    okExtend: {
      type: "primary",
      text: "下一步",
    },
    cancelExtend: {
      type: "primary",
      text: "上一步",
    },
  },
  {
    value: "4",
    cancel: () => {
      progress.value = "3";
    },
    ok: async () => {
      await infoFormRef.value?.submit();
      affirmOpen.value = true;
    },
    // ok按钮扩展
    okExtend: {
      type: "primary",
      text: "提交",
    },
    cancelExtend: {
      type: "primary",
      text: "上一步",
    },
  },
];
</script>

<style scoped lang="scss">
.container {
  // 样式区域
  &-id {
    display: flex;
    gap: 10rpx;
    &-item {
      display: inline-block;
    }
  }
  &-action {
    padding: 12rpx 24rpx;
  }
}
</style>
