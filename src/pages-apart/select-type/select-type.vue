<template>
  <ly-default title="选择人才类别" title-position="left">
    <view class="common-com common-pd container">
      <zk-radio-two
        v-model:value="talentType"
        :options="options"
      ></zk-radio-two>
    </view>
    <template #bottom>
      <view>
        <nut-button type="primary" block @click="methods.ok">确认</nut-button>
      </view>
    </template>
  </ly-default>
</template>

<script setup lang="ts">
import { usePathData } from "@/hook/usePathData";
import { options } from "./optData";
import { goToPage } from "@/router/topage";
const { eventData } = usePathData("pageData");
const talentType = ref("1");
const methods = {
  ok() {
    let pageurl = "";
    switch (talentType.value) {
      case "1":
        pageurl = "/pages-apart/talent-a/talent-a";
        break;
      case "2":
        pageurl = "/pages-apart/talent-b/talent-b";
        break;
      default:
        pageurl = "/pages-apart/talent-other/talent-other";
        break;
    }

    goToPage(pageurl, {
      ...unref(eventData),
      talentType: unref(talentType),
    });
  },
};
</script>

<style scoped lang="scss">
.container {
  // 样式区域
  overflow-y: auto;
}
</style>
