<script setup lang="ts">
import { useScrollView } from "@/hook/useScrollView";
import { apiMemberApartmentRecordPage } from "@/api/member";
import RecordItem from "./record-item.vue";
import { goToPage } from "@/router/topage";

// AuditStatusArray
const query = ref({
  state: 0,
});

// 计算属性查询参数
const params = computed(() => {
  return {
    state: useStateMap[query.value.state],
  };
});

const useStateMap = [null, 1, 2];
const {
  refresherStatus,
  onPullDownRefresh,
  onReachBottom,
  list,
  clearList,
  loading,
  reload,
  isEmpty,
} = useScrollView({
  api: apiMemberApartmentRecordPage,
  params,
});

onMounted(() => {
  watch(params, (v) => {
    clearList();
    reload();
  });
});

const methods = {
  // 查看详情
  goToInfo(item: any) {
    goToPage("/pages-apart/record-info/record-info", item, "event");
  },
};
</script>

<template>
  <ly-default
    :transparent="false"
    title="人才公寓填报记录"
    title-position="left"
  >
    <view class="common-com container">
      <view>
        <wd-tabs v-model="query.state" autoLineWidth>
          <wd-tab title="全部"> </wd-tab>
          <wd-tab title="待审核"> </wd-tab>
          <wd-tab title="已审核"> </wd-tab>
        </wd-tabs>
      </view>

      <scroll-view
        :scroll-y="true"
        class="common-scroll"
        :refresher-enabled="true"
        @refresherrefresh="onPullDownRefresh"
        :refresher-triggered="refresherStatus"
        @scrolltolower="onReachBottom"
      >
        <view class="container-list common-list">
          <RecordItem
            @click="methods.goToInfo(item)"
            v-for="item in list"
            :key="item.id"
            :item="item"
          >
          </RecordItem>
        </view>
        <view class="common-empty" v-if="isEmpty">
          <wd-status-tip image="content" tip="暂无内容" />
        </view>
        <zk-loading :loading="loading"></zk-loading>
      </scroll-view>
    </view>
  </ly-default>
</template>

<style scoped lang="scss">
.container {
  // 样式区域
  &-list {
    margin: 0;
  }
}
</style>
