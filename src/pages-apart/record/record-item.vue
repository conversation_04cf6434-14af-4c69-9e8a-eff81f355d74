<script setup lang="ts">
import { AuditStatusArray } from "@/maps/op";
import { matchMap } from "@/utils/tools";
import { get } from "lodash-es";
import type { ZkDesSchema } from "#/component";
const schemas: ZkDesSchema[] = [
  {
    field: "subTarget",
    label: "申请房型",
  },
  {
    field: "submitTime",
    label: "申请时间",
  },
];
interface Props {
  item: any;
}
const props = withDefaults(defineProps<Props>(), {
  item: () => ({}),
});
const emit = defineEmits(["click"]);
const cur = computed(() => {
  return matchMap(get(props.item, "state", ""), AuditStatusArray);
});
</script>

<template>
  <view @click="emit('click')">
    <zk-card
      icon="https://tzcjlw.oss-cn-shanghai.aliyuncs.com/mini-program/card/mansion.png"
      :title="get(item, 'apartmentName')"
    >
      <template #extra>
        <zk-tag :customColor="cur.color"> {{ cur.label }} </zk-tag>
      </template>
      <template #default>
        <view class="record-item-box">
          <image
            mode="aspectFill"
            class="record-item-image"
            :src="get(item, 'apartmentPhoto')"
          ></image>
          <view class="record-item-content">
            <zk-des :schemas="schemas" :data="item"></zk-des>
          </view>
        </view>
      </template>
      <template #footer>
        <view
          class="record-item-footer"
          v-if="get(cur, 'extend.show') && get(item, 'reasons')"
        >
          <zk-text :color="cur.color">
            <text> 原因: </text>
            <text>
              {{ get(item, "reasons") }}
            </text>
          </zk-text>
        </view>
      </template>
    </zk-card>
  </view>
</template>

<style scoped lang="scss">
.record-item {
  &-box {
    display: flex;
    gap: 24rpx;
    align-items: center;
  }
  &-image {
    width: 100rpx;
    height: 100rpx;
    border-radius: 14rpx;
  }
  &-content {
    flex-grow: 1;
  }
  &-footer {
    padding-bottom: 20rpx;
  }
}
</style>
