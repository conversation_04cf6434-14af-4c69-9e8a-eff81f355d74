<template>
  <ly-default title="公寓列表" title-position="left">
    <view class="common-com container">
      <scroll-view
        :scroll-y="true"
        class="common-scroll"
        :refresher-enabled="true"
        @refresherrefresh="onPullDownRefresh"
        :refresher-triggered="refresherStatus"
        @scrolltolower="onReachBottom"
      >
        <view class="container-list common-list">
          <ApartmentCard
            v-for="item in list"
            @onGo="methods.onGo(item)"
            :item="item"
            :key="item.id"
          />
          <view class="common-empty" v-if="isEmpty">
            <wd-status-tip image="content" tip="暂无内容" />
          </view>
          <zk-loading :loading="loading"></zk-loading>
        </view>
      </scroll-view>
    </view>
    <zk-affirm
      title="选择身份"
      :open="applyTypeAffirm"
      @on-ok="methods.ok()"
      @on-cancel="methods.cancel()"
      control
    >
      <view>
        <zk-radio
          v-model:value="applyType"
          :options="[
            {
              label: '个人',
              value: '1',
            },
            {
              label: '企业',
              value: '2',
            },
          ]"
        >
        </zk-radio>
      </view>
    </zk-affirm>
  </ly-default>
</template>

<script setup lang="ts">
import ApartmentCard from "./apartment-card.vue";
import { useScrollView } from "@/hook/useScrollView";
import { apiApartmentPage } from "@/api/apart";
import { back, goToPage } from "@/router/topage";

const {
  refresherStatus,
  onPullDownRefresh,
  onReachBottom,
  list,
  loading,
  isEmpty,
} = useScrollView({
  api: apiApartmentPage,
});

const applyType = ref("1");
const applyTypeAffirm = ref(true);
const methods = {
  onGo(item: any) {
    goToPage("/pages-apart/apartment-info/apartment-info", {
      ...item,
      applyType: unref(applyType),
    });
  },
  cancel() {
    back();
  },
  ok() {
    applyTypeAffirm.value = false;
  },
};
</script>

<style scoped lang="scss">
.container {
  // 样式区域
  &-list {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
  }
}
</style>
