<script setup lang="ts">
import { get } from "lodash-es";

interface Props {
  item: any;
}
const props = withDefaults(defineProps<Props>(), {
  item: () => ({}),
});

const emits = defineEmits(["onGo"]);
</script>

<template>
  <view class="apartment-card">
    <image
      class="apartment-card-image"
      :src="get(item, 'photo', '')"
      mode="aspectFill"
    ></image>
    <view class="apartment-card-box">
      <view class="apartment-card-box-left">
        <view> {{ get(item, "name") }} </view>
        <view class="apartment-card-box-des">
          {{ get(item, "address") }}
        </view>
      </view>
      <view class="apartment-card-box-right">
        <nut-button @click="emits('onGo')" size="small" type="primary">
          申请公寓
        </nut-button>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.apartment-card {
  height: 384rpx;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  &-image {
    width: 100%;
    height: 250rpx;
    flex-shrink: 0;
  }
  &-box {
    display: flex;
    align-items: center;
    padding: 0 24rpx;
    flex: 1;
    &-left {
      width: 0;
      display: flex;
      flex-direction: column;
      flex: 1;
      color: #1d2129;
      font-size: 30rpx;
    }
    &-right {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &-des {
      color: #999999;
      overflow: hidden; /* 隐藏超出的内容 */
      white-space: nowrap; /* 禁止换行 */
      text-overflow: ellipsis; /* 溢出时显示省略号 */
      font-size: 24rpx;
    }
  }
}
</style>
