{
  // 配置 调试第一个页面
  "condition": {
    "current": 1,
    // 当前激活的模式（list 的索引项）
    "list": [
      {
        "name": "调试",
        // 模式名称
        "path": "pages-talent/education-opinion/education-opinion"
        // 启动页面，必选
      },
      {
        "name": "结果",
        // 模式名称
        "path": "pages/ticket-result/ticket-result"
        // 启动页面，必选
      },
      {
        "name": "用户端核券信息",
        // 模式名称
        "path": "pages/ticket-info/ticket-info"
        // 启动页面，必选
      },
      {
        "name": "debug",
        // 模式名称
        "path": "pages/debug/debug"
        // 启动页面，必选
      },
      {
        "name": "debug6",
        // 模式名称
        "path": "pages/debug/debug6"
        // 启动页面，必选
      },
      {
        "name": "about",
        // 模式名称
        "path": "pages/about/about"
        // 启动页面，必选
      },
      {
        "name": "可使用的门店",
        // 模式名称
        "path": "pages/ticket-store/ticket-store",
        // 传递参数
        "query": "id=1906585095360872449"
      }
    ]
  },
  "pages": [
    //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "首页",
        "navigationStyle": "custom",
        "disableScroll": true
        // 下拉刷新
        //        "enablePullDownRefresh": true,
        // 触底加载
        //        "onReachBottomDistance": 80
      }
    },
    {
      "path": "pages/login/login",
      "style": {
        "navigationBarTitleText": "登录",
        "enablePullDownRefresh": false,
        "disableScroll": true
      }
    },
    {
      "path": "pages/about/about",
      "style": {
        "navigationBarTitleText": "关于",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/webcontent/webcontent",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/order-record/order-record",
      "style": {
        "navigationBarTitleText": "订单列表",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/edit-user/edit-user",
      "style": {
        "navigationBarTitleText": "编辑资料",
        "enablePullDownRefresh": false,
        "disableScroll": true
      }
    },
    {
      "path": "pages/debug/debug",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/debug/debug6",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/tem/tem",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/ticket/ticket",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/ticket-tips/ticket-tips",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/ticket-store/ticket-store",
      "style": {
        "navigationBarTitleText": "可使用的门店",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/ticket-info/ticket-info",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/ticket-result/ticket-result",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/ticket-record/ticket-record",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false,
        "disableScroll": true
      }
    },
    {
      "path": "pages/guide-info/guide-info",
      "style": {
        "navigationBarTitleText": "指南详情",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/consult-list/consult-list",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/consult-info/consult-info",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/store-enter/store-enter",
      "style": {
        "navigationBarTitleText": "入驻申请",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/auth-user/auth-user",
      "style": {
        "navigationBarTitleText": "完善个人信息认证",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/info-user/info-user",
      "style": {
        "navigationBarTitleText": "档案详情",
        "enablePullDownRefresh": false
      }
    }
  ],
  "subPackages": [
    {
      "root": "pages-talent",
      "pages": [
        {
          "path": "record/record",
          "style": {
            "navigationBarTitleText": "人才申请记录",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "education-subsidy/education-subsidy",
          "style": {
            "navigationBarTitleText": "人才申请记录",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "select-education/select-education",
          "style": {
            "navigationBarTitleText": "人才申请记录",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "education-opinion/education-opinion",
          "style": {
            "navigationBarTitleText": "意见页面",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "result/result",
          "style": {
            "navigationBarTitleText": "反馈页面",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "employment/employment",
          "style": {
            "navigationBarTitleText": "就业补贴",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "employment-record/employment-record",
          "style": {
            "navigationBarTitleText": "就业填报记录",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "employment-opinion/employment-opinion",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "record-info/record-info",
          "style": {
            "navigationBarTitleText": "申请详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "employment-record-info/employment-record-info",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      "root": "pages-apart",
      "pages": [
        {
          "path": "apartment-list/apartment-list",
          "style": {
            "navigationBarTitleText": "公寓列表",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "apartment-info/apartment-info",
          "style": {
            "navigationBarTitleText": "公寓详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "select-type/select-type",
          "style": {
            "navigationBarTitleText": "选择人才类别",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "talent-a/talent-a",
          "style": {
            "navigationBarTitleText": "人才类型 a",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "talent-other/talent-other",
          "style": {
            "navigationBarTitleText": "人才公寓申请",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "talent-b/talent-b",
          "style": {
            "navigationBarTitleText": "人才类型 b",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "talent-result/talent-result",
          "style": {
            "navigationBarTitleText": "申请结果",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "talent-ent/talent-ent",
          "style": {
            "navigationBarTitleText": "人才公寓申请企业",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "record/record",
          "style": {
            "navigationBarTitleText": "公寓填报记录",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "record-info/record-info",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        }
      ]
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "才聚蓝湾",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8",
    "navigationStyle": "custom",
    "disableScroll": true
  },
  "easycom": {
    "autoscan": true,
    "custom": {
      "^wd-(.*)": "wot-design-uni/components/wd-$1/wd-$1.vue",
      "^nut-(.*)?-(.*)": "nutui-uniapp/components/$1$2/$1$2.vue",
      "^nut-(.*)": "nutui-uniapp/components/$1/$1.vue",
      "^zk-(.*)": "@/components/zk-$1/$1.vue",
      "^ly-(.*)": "@/layout/ly-$1.vue"
    }
  }
}
