<script setup lang="ts">
import { initRouter } from '@/router/index'
import { onHide, onLaunch, onShow } from '@dcloudio/uni-app'
import { get } from 'lodash-es'
// import { useUserStore } from "@/store/user";
// import { useLoginStore } from "@/store/login";
//
// const userStore = useUserStore();
// const loginStore = useLoginStore();

onLaunch((option) => {
  console.log('App Launch', option)
  initRouter(get(option, 'path', ''))
})
onShow(() => {
  console.log('App Show')
  // if (loginStore.isLogin) {
  //   userStore.refreshUserInfo();
  // }
})
onHide(() => {
  console.log('App Hide')
})
uni.onUserCaptureScreen(() => {
  console.log('用户截屏了')
  return {
    query: '', // 通过截屏图片打开小程序的query参数
  }
})
</script>

<style lang="scss">
@import "nutui-uniapp/styles/index.scss";
@import "./global.scss";
page {
  background-color: #f6fafd;
  color: #333;
  height: 100vh;
  position: relative;
}
</style>
