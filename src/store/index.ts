import type { App } from "vue";
import { createPersistedState } from "pinia-plugin-persistedstate";
const store = createPinia();
export function setupStore(app: App<Element>) {
  app.use(store);
  // 持久化pinia
  store.use(
    createPersistedState({
      storage: {
        setItem(key, value) {
          uni.setStorageSync(key, value);
        },
        getItem(key) {
          return uni.getStorageSync(key);
        },
      },
      // serializer: {
      //   serialize: (value) => JSON.stringify(value),
      //   deserialize: (value) => JSON.parse(value),
      // },
    }),
  );
  return store;
}
export { store };
