import { store } from "@/store";
import { get, every } from "lodash-es";
import { getUserInfo } from "@/api/user";
import type { UserState } from "#/user";
import { maskPhoneNumber } from "@/utils/tools";
import { apiGetOpenPosterList } from "@/api/open";

export const useUserStore = defineStore(
  "user",
  () => {
    const state = reactive<UserState>({
      userId: "",
      type: "",
      username: "",
      phone: "",
      nickname: "",
      avatar: "",
      sex: null,
      education: null,
      enterpriseName: null,
      audit: null,
      storeReasons: null, // 审核不通过原因 「商家」
      memberReasons: null, // 审核不通过原因 「用户」
      auth: null,
      swiperList: [],
      // swiper更新时间
      swiperTime: 0,
      operationAudit: null,
    });

    const setUserInfo = (data: any) => {
      const type = get(data, "type", "");
      if (type === "mini") {
        state.userId = get(data, "id", "");
        state.type = get(data, "type", "");
        state.phone = get(data, "phone", "");
        state.username = get(data, "data.name", "");
        state.nickname = get(data, "data.nickname", "");
        state.avatar = get(data, "data.photo", "");
        state.education = get(data, "data.education", null);
        state.sex = get(data, "data.sex", null);
        state.enterpriseName = get(data, "data.enterpriseName", null);
        state.auth = get(data, "data.auth", null);
        state.memberReasons =
          get(data, "data.operationAudit.reasons", "未填写") || "未填写";
        state.operationAudit = get(data, "data.operationAudit", null);
      } else if (type === "store") {
        state.userId = get(data, "id", "");
        state.type = get(data, "type", "");
        state.phone = get(data, "phone", "");
        state.nickname = get(data, "data.title", "");
        state.username = get(data, "data.linkman", "");
        state.avatar = get(data, "data.portrait", "");
        state.audit = get(data, "data.audit", null);
        state.storeReasons =
          get(data, "data.operationAudit.reasons", "未填写") || "未填写";

        if (get(data, "data.operationAudit", null)) {
          state.operationAudit = {
            ...get(data, "data.operationAudit", null),
            phone: get(data, "data.phone", ""),
            photo: get(data, "data.photo", ""),
            title: get(data, "data.title", ""),
            address: get(data, "data.address", ""),
            categoryIds: get(data, "data.categoryIds", ""),
            linkman: get(data, "data.linkman", ""),
          };
        } else {
          state.operationAudit = null;
        }
      }
    };
    const isStore = computed(() => state.type === "store");

    const resetState = () => {
      state.userId = "";
      state.type = "";
      state.username = "";
      state.nickname = "";
      state.avatar = "";
      state.phone = "";
      state.education = null;
      state.sex = null;
      state.audit = null;
      state.storeReasons = null;
      state.memberReasons = null;
      state.auth = null;
      state.operationAudit = null;
    };

    const refreshUserInfo = async () => {
      try {
        const res = await getUserInfo();
        console.log("refreshUserInfo", res);
        setUserInfo(res);
      } catch (error) {
        // 登录过期
        return Promise.reject(error);
      }
    };

    // 显示头像实际 URL 计算属性
    const avatarUrl = computed(() => {
      return (
        state.avatar ||
        "https://tzcjlw.oss-cn-shanghai.aliyuncs.com/mini-program/user-ar.png"
      );
    });

    // 脱敏用户信息
    const maskUserName = computed(() => {
      if (state.nickname) {
        return maskPhoneNumber(state.nickname);
      }
      return "未设置昵称";
    });

    // const isInfo = computed(() => {
    //   if (
    //     every(
    //       [state.education, state.sex, state.enterpriseName, state.username],
    //       Boolean,
    //     )
    //   ) {
    //     return true;
    //   }
    //   return false;
    // });
    //

    const isAuth = computed(() => {
      if (state.auth === "success") {
        return true;
      }
      return false;
    });

    // 获取轮播图
    const getSwiperList = async () => {
      state.swiperList = (await apiGetOpenPosterList()) || [];
      state.swiperTime = Date.now();
    };

    return {
      ...toRefs(state),
      setUserInfo,
      resetState,
      refreshUserInfo,
      isStore,
      avatarUrl,
      maskUserName,
      isAuth,
      getSwiperList,
    };
  },
  {
    persist: true,
  },
);

// Need to be used outside the setup
export function useUserStoreWithOut() {
  return useUserStore(store);
}
