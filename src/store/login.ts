import { store } from "@/store";
import { useUserStore } from "@/store/user";
import { apiAuthLogout, apiLoginApi } from "@/api/login";
import type { LoginParams, ClientId } from "#/login";
import { get } from "lodash-es";
import { back } from "@/router/topage";

interface LoginState {
  token?: string;
  lastLoginTime?: string;
  clientId?: ClientId;
}

export const useLoginStore = defineStore(
  "login",
  () => {
    const userStore = useUserStore();
    const state = reactive<LoginState>({
      token: undefined,
      lastLoginTime: undefined,
      clientId: undefined,
    });
    const sendsmsTime = ref(0);

    const isWatermark = ref<boolean>(
      get(import.meta.env, "VITE_WATERMARK", "false") === "true",
    );

    const getToken = computed(() => {
      return state.token;
    });
    const isLogin = computed(() => {
      return !!getToken.value;
    });

    const setToken = (token: string) => {
      state.token = token;
    };

    const setClientId = (clientId?: ClientId) => {
      state.clientId = clientId;
    };

    const logout = () => {
      apiAuthLogout().finally(() => {
        resetState();
        userStore.resetState();
      });
    };

    const login = async (params: LoginParams) => {
      try {
        uni.showLoading({
          title: "登录中",
          mask: true,
          // 不关
          duration: 0,
        });
        const res = await apiLoginApi(params);
        setToken(get(res, "accessToken.tokenValue", ""));
        setClientId(params.clientId);
        // 获取用户信息
        await userStore.refreshUserInfo();
        uni.hideLoading();
        uni.showToast({
          title: "登录成功",
        });
        back();
      } catch (e) {
        uni.hideLoading();
        console.log("错误信息");
        const msg = get(e, "msg", "登录失败");
        uni.showToast({
          title: msg,
          icon: "error",
        });
      }
    };
    // 刷新发送时间
    const refreshSendsmsTime = () => {
      sendsmsTime.value = Date.now() + 120000;
    };

    // 重置
    const resetState = () => {
      state.token = undefined;
      state.lastLoginTime = undefined;
      state.clientId = undefined;
      sendsmsTime.value = 0;
    };

    return {
      ...toRefs(state),
      isLogin,
      getToken,
      setToken,
      logout,
      setClientId,
      login,
      sendsmsTime,
      refreshSendsmsTime,
      resetState,
      isWatermark,
    };
  },
  {
    persist: true,
  },
);

// Need to be used outside the setup
export function useLoginStoreWithOut() {
  return useLoginStore(store);
}
