import { find } from "lodash-es";
import { getUserInfo } from "@/api/user";
import { PageEnum } from "@/enums/pageEnum";

interface Client {
  clientId: string;
  clientSecret: string;
  clientName: string;
  label: string;
  value: string;
  scope: string;
  apiUserInfo: () => Promise<any>;
  // apiPassWord: (data: any) => Promise<any>;
  homePath: string;
  // apiUserInfoExtend?: (params: any) => Promise<any>;
  Authorization: string;
}

export const clientList: Client[] = [
  {
    clientId: "mini",
    clientSecret: "mini",
    clientName: "用户",
    label: "用户",
    value: "mini",
    scope: "server",
    apiUserInfo: getUserInfo,
    homePath: PageEnum.BASE_HOME,
    Authorization: `Basic bWluaTptaW5p`,
  },
  {
    clientId: "store",
    clientSecret: "store",
    clientName: "商家",
    label: "商家",
    value: "store",
    scope: "server",
    apiUserInfo: getUserInfo,
    homePath: PageEnum.BASE_HOME,
    Authorization: `Basic c3RvcmU6c3RvcmU=`,
  },
];

export function getClient(clientId: string): Client | undefined {
  const foundClient = find(clientList, { clientId }) as Client;
  if (!foundClient) return undefined;
  return foundClient;
}
