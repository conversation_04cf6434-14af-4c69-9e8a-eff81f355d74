// 导入
import { useLoginStoreWithOut } from "@/store/login";
import { getQueryString } from "./helper";
import { uniqueId } from "lodash-es";
import { handleStatusError, handleError, showToastMsg } from "./checkStatus";

const commoneRequestUrl = import.meta.env.VITE_BASEURL; // 接口统一访问链接
console.log("接口统一访问链接", import.meta.env);
// 请求状态 map
const requestMap = new Map<string, Request.requestMap>();
let loadingStatus = false;

// 接口封装
function http(data1: Request.httpType): Promise<any> {
  // Promise 异步编程的一种解决方案
  const {
    okmsg = "",
    errmsg = "",
    method = "GET",
    url,
    showToast = false,
    headers = {},
    data,
    isAuth = true,
  } = data1;

  // 完整的 url
  const fullUrl = getQueryString(data1);
  const requestId = uniqueId("request_");
  requestMap.set(requestId, { ...data1 });
  console.log("请求参数", data1);
  console.log("请求地址", `${commoneRequestUrl}${fullUrl}`);
  const promise = new Promise((resolve, reject) => {
    (async () => {
      const loginStore = useLoginStoreWithOut();
      try {
        // 获取token
        const jwtToken = loginStore.getToken;
        // 显示loading
        refreshLoadingStatus();

        const header: Record<string, string> = {
          "Content-Type": "application/json;charset=UTF-8", // 内容类型(默认 application/json;charset=UTF-8)
          ...headers,
        };

        if (jwtToken && !header.Authorization && isAuth)
          header.Authorization = `Bearer ${jwtToken}`;

        uni.request({
          url: `${commoneRequestUrl}${fullUrl}`, // 拼接访问接口完整地址
          data: data || {}, // 向后端传递的数据(默认空对象{})
          method, // 接口类型(默认GET请求)
          header: header,
          success(res) {
            requestMap.delete(requestId);
            refreshLoadingStatus();
            const { statusCode } = res;
            if (statusCode !== 200)
              return handleStatusError(res as Request.resType, reject);

            const { data, code } = res.data as Request.resDataType;

            if (code === 200 || code === 0) {
              showToastMsg({ showToast, msg: okmsg, isSuccess: true });
              resolve(data);
            } else {
              console.log("error 捕捉错误", res);
              return handleError(
                res.data as Request.resDataType,
                reject,
                data1,
              );
            }
          },
          fail(err: any) {
            requestMap.delete(requestId);
            refreshLoadingStatus();
            showToastMsg({ showToast, msg: errmsg, isSuccess: false });
            reject(err);
          },
          complete() {
            // 接口调用结束
          },
        });
      } catch (error) {
        console.log("error 捕捉错误 外部", url, error);
        reject(error);
      }
    })();
  });
  return promise;
}

// 刷新 loading 状态
function refreshLoadingStatus() {
  const requestList = Array.from(requestMap.values());
  // 查找 requestList isLoading 为 true 的元素
  const isLoading = requestList.some((item) => item.isLoading);
  if (isLoading && !loadingStatus) {
    uni.showLoading({
      title: "加载中",
      mask: true,
      success: () => {
        loadingStatus = true;
      },
      fail: () => {
        loadingStatus = false;
      },
    });
  } else if (!isLoading && loadingStatus) {
    loadingStatus = false;
    uni.hideLoading();
  }
}

export { http };
