import { isString, mapValues } from "lodash-es";

export function decodeObjectRecursive(
  obj: Record<string, any> | undefined,
): Record<string, any> {
  if (obj === undefined) {
    return {};
  }
  return mapValues(obj, (value) => {
    if (isString(value)) {
      return decodeURIComponent(value); // 解码字符串
    } else if (value && typeof value === "object" && !Array.isArray(value)) {
      return decodeObjectRecursive(value); // 递归处理嵌套对象
    } else {
      return value; // 其他类型保持原样
    }
  });
}

// export function decodeObjectRecursive(
//   obj: Record<string, any> | undefined,
// ): Record<string, any> {
//   if (obj === undefined) {
//     return {};
//   }
//   return mapValues(obj, (value) => {
//     if (isString(value)) {
//       return decodeURIComponent(value);
//     } else if (typeof value === "object" && value !== null) {
//       return decodeObjectRecursive(value); // 递归处理嵌套对象
//     } else {
//       return value; // 其他类型保持原样
//     }
//   });
// }
// 示例数据
// const nestedInput = {
//     url: "https%3A%2F%2Fwww.baidu.com",
//     param: "test%20value",
//     count: 42,
//     nested: {
//         innerUrl: "https%3A%2F%2Fexample.com",
//         innerParam: "nested%20value",
//     },
// };
//
// const decodedNested = decodeObjectRecursive(nestedInput);
//
// console.log(decodedNested);
// 输出: {
//   url: "https://www.baidu.com",
//   param: "test value",
//   count: 42,
//   nested: {
//     innerUrl: "https://example.com",
//     innerParam: "nested value",
//   },
// }
