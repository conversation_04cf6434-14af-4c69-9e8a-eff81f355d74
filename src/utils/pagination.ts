// 导入依赖

import { omit } from "lodash-es";

interface PageResponse<T> {
  data: T[];
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}
interface AxiosResponse<T> {
  data: T;
  code: number;
  msg: string;
}
export class Pagination<T> {
  private currentPageData: T[] = [];
  private apiMethod: (pagination: any) => Promise<any>;
  private pageNo: number = 1; // 当前页码
  private pageSize: number = 10; // 每页数据大小
  private total: number = 0; // 总条数
  private totalPage: number = 0; // 总页数
  private params: any = {}; // 请求参数
  private extra: any = {}; // 扩展参数

  constructor(apiMethod: (pagination: any) => Promise<any>, params?: any) {
    this.apiMethod = apiMethod;
    if (params) {
      this.params = params;
    }
  }

  // 刷新方法
  public async refresh(_params?: any): Promise<T[]> {
    this.pageNo = 1; // 重置为第一页
    if (_params) {
      this.params = _params;
    }
    const response = await this.apiMethod(
      Object.assign(
        {
          current: this.pageNo,
          size: this.pageSize,
        },
        this.params,
      ),
    );
    if (response) {
      console.log("response", response);
      this.currentPageData = response.records;
      this.total = response.total;
      this.totalPage = response.pages;
      this.pageNo = response.current;
      this.extra = omit(response, ["records", "total", "pages", "current"]);
      // 对pageNo进行校验
      if (this.pageNo > this.totalPage) {
        this.pageNo = this.totalPage;
      }
    }

    return this.currentPageData;
  }

  // 翻页方法
  public async nextPage(): Promise<T[]> {
    this.pageNo++;
    const response = await this.apiMethod(
      Object.assign(
        {
          current: this.pageNo,
          size: this.pageSize,
        },
        this.params,
      ),
    );
    if (response) {
      this.currentPageData = this.currentPageData.concat(response.records);
      this.total = response.total;
      this.totalPage = response.pages;
      this.pageNo = response.current;
      this.extra = omit(response, ["records", "total", "pages", "current"]);
      // 对pageNo进行校验
      if (this.pageNo > this.totalPage) {
        this.pageNo = this.totalPage;
      }
    }

    return this.currentPageData;
  }

  // 上一页数据和下一页数据连接方法
  public getConnectedData(): T[] {
    return this.currentPageData;
  }

  // 获取当前页码
  public getPage(): number {
    return this.pageNo;
  }

  // 获取每页数据大小
  public getPageSize(): number {
    return this.pageSize;
  }
  // 是否有下一页
  public hasNextPage(): boolean {
    console.log(
      "this.pageNo",
      this.pageNo >= this.totalPage,
      this.pageNo,
      this.totalPage,
    );
    return this.pageNo >= this.totalPage;
  }

  // 获取扩展信息
  public getExtra(): any {
    return this.extra;
  }

  // 获取总条数
  public getTotal(): number {
    return this.total;
  }

}
// export { Pagination }

// // 示例使用
// const apiMethod = async (): Promise<AxiosResponse<PageResponse<number>>> => {
// 	const response = await axios.get<PageResponse<number>>('/api/page', {
// 		params: {
// 			page: pagination.getPage(),
// 			pageSize: pagination.getPageSize(),
// 		},
// 	})
// 	return response
// }

// // 创建分页实例
// const pagination = new Pagination<number>(apiMethod)

// // 刷新数据
// pagination.refresh().then((data) => {
// 	console.log(data)
// })

// // 翻页
// pagination.nextPage().then((data) => {
// 	console.log(data)
// })

// // 获取连接的数据
// const connectedData = pagination.getConnectedData()
// console.log(connectedData)

// // 获取当前页码和每页数据大小
// const currentPage = pagination.getPage()
// const pageSize = pagination.getPageSize()
// console.log(currentPage, pageSize)
