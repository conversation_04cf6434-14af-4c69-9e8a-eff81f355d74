import type {
  CreateMapParams,
  CreateMapReturn,
  ExtendCreateMapReturnValue,
} from "#/tools";
import { map, find } from "lodash-es";

export function createMap(list: CreateMapParams[]): CreateMapReturn {
  return map(list, ([value, label, color, extend]) => ({
    value,
    label,
    color,
    extend,
    text: label,
  }));
}

// 匹配 map 配合 createMap 使用
export function matchMap(
  value: string | number | undefined,
  listArr: CreateMapReturn,
): ExtendCreateMapReturnValue {
  const item = find(listArr, { value });
  if (!item) {
    return {
      value: "",
      label: "未知",
      text: "未知",
      color: "",
      extend: {},
      success: false,
    };
  }
  return {
    ...item,
    success: true,
  };
}

export function matchMapLabel(
  value: string | number | undefined,
  listArr: CreateMapReturn,
): string {
  const item = find(listArr, { value });
  if (!item) {
    return "--";
  }
  return item.label;
}

export function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export function StrToJson(str: string): Record<string, any> {
  if (isJson(str)) {
    return JSON.parse(str);
  } else {
    return {};
  }
}

export function isJson(str: string) {
  if (typeof str == "string") {
    try {
      const obj = JSON.parse(str);
      if (typeof obj == "object" && obj) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      console.log("error：" + str + "!!!" + e);
      return false;
    }
  }
  return false;
}

export function maskPhoneNumber(input: string) {
  if (!input) {
    return input;
  }
  // 定义手机号正则（以中国大陆手机号为例）
  const phoneRegex = /^1[3-9]\d{9}$/;

  // 判断是否为手机号
  if (phoneRegex.test(input)) {
    // 如果是手机号，进行中间脱敏
    return input.replace(/^(\d{3})\d{4}(\d{4})$/, "$1****$2");
  } else {
    // 如果不是手机号，返回原始字符串
    return input;
  }
}
