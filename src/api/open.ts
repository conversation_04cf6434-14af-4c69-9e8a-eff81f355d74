import { http } from "@/utils/http/request";
enum Api {
  OpenPolicyGet = "/rest/operation/open/policy/get",
  OpenConsultPage = "/rest/operation/open/consult/pageInfo",
  OpenPosterList = "/rest/operation/open/poster/list",
}

// 获取政策
export function getOpenPolicyGet(params: any) {
  return http({
    url: Api.OpenPolicyGet,
    params,
    method: "GET",
  });
}

// 获取咨询列表
export function apiGetOpenConsult(params: any) {
  return http({
    url: Api.OpenConsultPage,
    params,
    method: "GET",
    isAuth: false,
  });
}

// 导出一个函数，用于获取开放海报列表
export function apiGetOpenPosterList() {
  return http({
    url: Api.OpenPosterList,
    method: "GET",
    isAuth: false, // 不需要鉴权
  });
}
