import { http } from "@/utils/http/request";
// import { ContentTypeEnum } from "@/enums/httpEnum";
import { getClient } from "@/config/clientSetting";
import type { LoginParams } from "#/login";
import { omit } from "lodash-es";

enum Api {
  // OpenLogin = "/rest/open/login",
  GetPhoneNumber = "/rest/operation/open/getPhoneNumber/{code}",
  OauthToken = "/oauth2/token",
  AuthLogout = "/auth/logout",
  SmsSend = "/rest/sms/send/{mobile}",
}
/**
 * @description: 获取微信用户信息
 */
// export function OpenloginApi(data: any) {
//   return http({
//     url: Api.OpenLogin,
//     data,
//     method: "POST",
//     headers: {
//       "Content-Type": ContentTypeEnum.FORM_URLENCODED,
//     },
//   });
// }

// 获取用户手机号登录
export function getPhoneNumber(params: any) {
  return http({
    url: Api.GetPhoneNumber.replace("{code}", params.code),
    method: "GET",
    headers: {
      Authorization: `Basic bWluaTptaW5p`,
    },
  });
}

// oauth2登录
export function apiLoginApi(params: LoginParams) {
  const client = getClient(params.clientId);
  console.log("客户端信息", client, params);
  if (!client) {
    return Promise.reject("未找到客户端信息");
  }
  return http({
    url: Api.OauthToken,
    method: "POST",
    params: {
      scope: client.scope,
      ...omit(params, ["clientId"]),
    },
    headers: {
      Authorization: client.Authorization,
    },
  });
}
// 退出登录
export function apiAuthLogout() {
  return http({
    url: Api.AuthLogout,
    method: "DELETE",
  });
}

// 发送短信
export function smsSendApi(params: any) {
  return http({
    url: Api.SmsSend.replace("{mobile}", params.mobile),
    method: "POST",
    isLoading: true,
    showToast: true,
    okmsg: "发送成功",
    errmsg: "发送失败",
  });
}
