import { http } from "@/utils/http/request";

enum Api {
  StoreVerification = "/rest/operation/store/verification/{id}",
  StoreRecord = "/rest/operation/store/record",
  StoreListType = "/rest/operation/store/listType",
  StoreApply = "/rest/operation/store/apply",
}

export function apiStoreVerification({ id }: any) {
  return http({
    url: Api.StoreVerification.replace("{id}", id),
    method: "PUT",
    isLoading: true,
    showToast: true,
  });
}

export function apiStoreRecord(params: any) {
  return http({
    url: Api.StoreRecord,
    params,
    method: "GET",
  });
}

// 获取商家类型
export function apiStoreListType() {
  return http({
    url: Api.StoreListType,
    method: "GET",
  });
}

// 申请商家
export function apiStoreApply(params: any) {
  return http({
    url: Api.StoreApply,
    data: params,
    method: "POST",
  });
}
