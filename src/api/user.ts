import { http } from '@/utils/http/request'

enum Api {
  UpdateUserInfo = '/rest/customers/updateCustomer',
  GetUserInfo = '/rest/operation/member/loginInfo',
  MemberAuth = '/rest/operation/member/auth',
}

// 用户更新
export function updateUserInfo(params: any) {
  return http({
    url: Api.UpdateUserInfo,
    data: params,
    method: 'PUT',
    showToast: true,
    isLoading: true,
  })
}

// 获取用户信息
export function getUserInfo() {
  return http({
    url: Api.GetUserInfo,
    method: 'GET',
  })
}

// 提交用户认证信息
export function apiMemberAuth(params: any) {
  return http({
    url: Api.MemberAuth,
    data: params,
    method: 'POST',
  })
}
