import { http } from "@/utils/http/request";

enum Api {
  MemberRecord = "/rest/operation/member/record",
  MemberReceive = "/rest/operation/member/receive/{couponId}",
  MemberGenerateEducation = "/rest/operation/member/generateEducation",
  MemberApplyEducation = "/rest/operation/member/applyEducation",
  MemberApplyEmployment = "/rest/operation/member/applyEmployment",
  MemberGenerateEmployment = "/rest/operation/member/generateEmployment",
  MemberGetApplyStage = "/rest/operation/member/getApplyStage/{type}",
  MemberEmploymentRecordPage = "/rest/operation/member/employmentRecordPage",
  MemberEducationRecordPage = "/rest/operation/member/educationRecordPage",
  MemberApartmentRecordPage = "/rest/operation/member/apartmentRecordPage",
  MemberCurrent = "/rest/operation/member/current",
}

export function apiMemberRecordPage(params: any) {
  return http({
    url: Api.MemberRecord,
    params,
    method: "GET",
  });
}

export function apiMemberReceive({ id }: any) {
  return http({
    url: Api.MemberReceive.replace("{couponId}", id),
    method: "PUT",
    isLoading: true,
  });
}

// 人才学历补贴
export function apiMemberApplyEducation(data: any) {
  return http({
    url: Api.MemberApplyEducation,
    method: "POST",
    data,
    showToast: true,
  });
}

// 生成人才学历补贴文件
export function apiMemberGenerateEducation(data: any) {
  return http({
    url: Api.MemberGenerateEducation,
    method: "POST",
    data,
  });
}

// 人才就业补贴
export function apiMemberApplyEmployment(data: any) {
  return http({
    url: Api.MemberApplyEmployment,
    method: "POST",
    data,
    showToast: true,
  });
}

// 生成人才就业补贴文件
export function apiMemberGenerateEmployment(data: any) {
  return http({
    url: Api.MemberGenerateEmployment,
    method: "POST",
    data,
  });
}

// 获取暂存信息
export function apiMemberGetApplyStage(type: any) {
  return http({
    url: Api.MemberGetApplyStage.replace("{type}", type),
    method: "GET",
  });
}

// 人才就业补贴记录
export function apiMemberEmploymentRecordPage(params: any) {
  return http({
    url: Api.MemberEmploymentRecordPage,
    params,
    method: "GET",
  });
}

// 人才学历补贴记录
export function apiMemberEducationRecordPage(params: any) {
  return http({
    url: Api.MemberEducationRecordPage,
    params,
    method: "GET",
  });
}

// 人才公寓申请记录
export function apiMemberApartmentRecordPage(params: any) {
  return http({
    url: Api.MemberApartmentRecordPage,
    params,
    method: "GET",
  });
}

// 修改用户资料
export function apiMemberCurrent(data: any) {
  return http({
    data,
    url: Api.MemberCurrent,
    method: "PUT",
  });
}

// 修改用户资料
export function apiMemberCurrentLoading(data: any) {
  return http({
    data,
    url: Api.MemberCurrent,
    method: "PUT",
    isLoading: true,
    showToast: true,
  });
}
