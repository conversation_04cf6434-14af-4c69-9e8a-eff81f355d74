import { http } from "@/utils/http/request";

enum Api {
  ApartmentPage = "/rest/operation/open/apartment/pageInfo",
  applyApartment = "/rest/operation/member/applyApartment",
}

export function apiApartmentPage(params: any) {
  return http({
    url: Api.ApartmentPage,
    method: "GET",
    params,
  });
}

export function apiApplyApartment(data: any) {
  return http({
    url: Api.applyApartment,
    method: "POST",
    data,
    showToast: true,
  });
}
