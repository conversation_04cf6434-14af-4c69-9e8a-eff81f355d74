import { type Ref, ref, unref, watch } from "vue";
import { useToast } from "wot-design-uni";

interface UseApiLoadingReturn<T = any> {
  loading: Ref<boolean>;
  reload: (_params?: any) => Promise<T>;
  apiResult: Ref<T | undefined>;
}

export function useApiLoading<T = any>({
  api,
  params,
  immediate = true,
  watchParams = true,
  toastLoading = false,
  loadingDefault = false,
  valueDefault,
}: {
  api: (...arg: any[]) => Promise<T>;
  params?: Ref<any> | Recordable;
  immediate?: boolean;
  watchParams?: boolean;
  toastLoading?: boolean;
  loadingDefault?: boolean;
  valueDefault?: T;
}): UseApiLoadingReturn<T> {
  const loading = ref(loadingDefault);
  const apiResult = ref<T | undefined>();
  apiResult.value = valueDefault;
  const toast = useToast();
  loadingDefault && toast.loading("加载中...");
  const reload = async (_params?: any) => {
    try {
      loading.value = true;
      toastLoading && toast.loading("加载中...");
      const queryParams = _params || params;
      apiResult.value = await (queryParams ? api(unref(queryParams)) : api());
      return Promise.resolve(apiResult.value);
    } catch (e) {
      return Promise.reject(e);
    } finally {
      loading.value = false;
      toastLoading && toast.close();
    }
  };

  if (watchParams && params) {
    watch(
      () => params,
      () => {
        reload();
      },
    );
  }
  immediate && reload();

  return {
    loading,
    reload,
    apiResult,
  };
}
