import { unref, type Ref } from "vue";
import { Pagination } from "@/utils/pagination";
import { size } from "lodash-es";

export type ScrollViewStatusType = "loadmore" | "loading" | "nomore" | "error";

export function useScrollView<T>({
  api,
  params,
  immediate = true,
}: {
  api: (params?: any) => Promise<T[]>;
  params?: Ref<any> | Record<string, any>;
  immediate?: boolean;
}) {
  const refresherStatus = ref(false);
  const status = ref<ScrollViewStatusType>("loadmore");
  const list = ref<T[]>([]);
  const extra = ref<any>({});
  const pagination = new Pagination<T>(api);
  const loading = ref<boolean>(false);
  const total = ref<number>(0);
  const isEmpty = computed(() => {
    return !size(list.value);
  });

  const onPullDownRefresh = async () => {
    try {
      status.value = "loading";
      loading.value = true;
      refresherStatus.value = true;
      if (unref(params)) {
        list.value = await pagination.refresh(unref(params));
      } else {
        list.value = await pagination.refresh();
      }
      extra.value = pagination.getExtra();
      total.value = pagination.getTotal();
      const hasNextPage = pagination.hasNextPage();
      if (hasNextPage) {
        status.value = "nomore";
      } else {
        status.value = "loadmore";
      }
    } catch (error) {
      console.log(error);
      status.value = "error";
    } finally {
      // 获取分页
      loading.value = false;
      refresherStatus.value = false;
    }
  };

  const onReachBottom = async () => {
    if (status.value === "nomore") {
      return;
    }
    try {
      status.value = "loading";
      list.value = await pagination.nextPage();
      const hasNextPage = pagination.hasNextPage();
      extra.value = pagination.getExtra();
      total.value = pagination.getTotal();
      if (hasNextPage) {
        status.value = "nomore";
      } else {
        status.value = "loadmore";
      }
    } catch (error) {
      console.log(error);
      status.value = "error";
    } finally {
      // 获取分页
      refresherStatus.value = false;
    }
  };

  const clearList = () => {
    list.value = [];
    total.value = 0;
  };

  const reload = async () => {
    try {
      status.value = "loading";
      loading.value = true;
      if (unref(params)) {
        list.value = await pagination.refresh(unref(params));
      } else {
        list.value = await pagination.refresh();
      }
      extra.value = pagination.getExtra();
      total.value = pagination.getTotal();
      const hasNextPage = pagination.hasNextPage();
      if (hasNextPage) {
        status.value = "nomore";
      } else {
        status.value = "loadmore";
      }
    } catch (error) {
      console.log(error);
      status.value = "error";
    } finally {
      loading.value = false;
    }
  };

  immediate && reload();

  return {
    refresherStatus,
    status,
    pagination,
    onPullDownRefresh,
    onReachBottom,
    list,
    total,
    clearList,
    extra,
    loading,
    reload,
    isEmpty,
  };
}
