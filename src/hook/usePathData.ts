import {
  type Ref,
  type ShallowRef,
  getCurrentInstance,
  shallowRef,
  ref,
  unref,
} from "vue";
import { cloneDeep } from "lodash-es";
import { onLoad } from "@dcloudio/uni-app";
import { decodeObjectRecursive } from "@/utils/querytool";

interface UsePathDataReturn<T = any, K = any> {
  params: Ref<T | undefined>;
  eventData: Ref<K | undefined>;
  options: ShallowRef<any>;
  eventChannel: ShallowRef<any | undefined>;
}

interface UsePathDataParams<T = any, K = any> {
  params: T | undefined;
  eventData: K | undefined;
  options: any;
  eventChannel?: any;
}

export function usePathData<T = any, K = any>(
  key?: string,
  cb?: (v: UsePathDataParams<T, K>) => void,
): UsePathDataReturn<T, K> {
  const _params = ref<T>();
  const _data = ref<K>();
  const _options = shallowRef<any>();
  const eventChannel = shallowRef<any>();
  onLoad((opt) => {
    _options.value = opt;
    _params.value = decodeObjectRecursive(opt) as T;
    const proxy = getCurrentInstance()?.proxy as any;
    const _eventChannel = proxy && proxy?.getOpenerEventChannel();
    const invokeCallback = () => {
      console.log("触发");
      cb?.({
        options: unref(_options),
        params: unref(_params),
        eventData: unref(_data),
        eventChannel: unref(eventChannel),
      });
    };
    if (_eventChannel && _eventChannel?.on) {
      eventChannel.value = _eventChannel;
      if (key) {
        _eventChannel.on(key, (res: any) => {
          _data.value = cloneDeep(res);
          invokeCallback();
        });
      } else {
        invokeCallback();
      }
    } else {
      invokeCallback();
    }
  });
  return {
    options: _options,
    params: _params,
    eventData: _data,
    eventChannel,
  };
}
