import { unref, type Ref, ref } from "vue";
import { size } from "lodash-es";

export function useScrollViewApi<T>({
  api,
  params,
  immediate = true,
}: {
  api: (params?: any) => Promise<T>;
  params?: Ref<any> | Record<string, any>;
  immediate?: boolean;
}) {
  const refresherStatus = ref(false);
  const apiResult = ref<T | undefined>();
  const loading = ref<boolean>(false);
  const isEmpty = computed(() => {
    return !size(apiResult.value as any);
  });

  const reload = async () => {
    try {
      loading.value = true;
      apiResult.value = await (params ? api(params.value) : api());
      return Promise.resolve(apiResult.value);
    } catch (e) {
      return Promise.reject(e);
    } finally {
      loading.value = false;
    }
  };

  const onPullDownRefresh = async () => {
    try {
      refresherStatus.value = true;
      await reload();
    } finally {
      refresherStatus.value = false;
    }
  };

  const onReachBottom = async () => {};

  immediate && reload();

  return {
    refresherStatus,
    onPullDownRefresh,
    onReachBottom,
    loading,
    reload,
    isEmpty,
    apiResult,
  };
}
