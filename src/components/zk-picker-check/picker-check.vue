<script setup lang="ts">
import { useRuleFormItem } from "@/hook/useFormItem";
import { find, first, size } from "lodash-es";
import type { CheckboxGroupInst } from "nutui-uniapp";

const group = ref<CheckboxGroupInst | null>(null);

type ValueState = string | number;

interface Option {
  text: string;
  value: string;
}

interface Props {
  open?: boolean;
  value?: ValueState;
  options: Option[];
  // value 格式化
  placeholder?: string;
  show?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  open: false,
  placeholder: "选择",
  options: () => [],
  show: true,
});

const emit = defineEmits(["update:value", "change", "update:open", "ok"]);
const [state] = useRuleFormItem<Props, keyof Props, ValueState>(
  props,
  "value",
  ["update:value", "change"],
);

// 参数中转
const renderValue = computed({
  get() {
    if (state.value) {
      // 字符串采用逗号分割
      if (typeof state.value === "string") {
        const ret = state.value.split(",");
        return ret;
      } else {
        return [state.value];
      }
    }
    return [];
  },
  set(val: any) {
    if (size(val)) {
      state.value = val.join(",");
      console.log("state.value", state.value);
    } else {
      state.value = "";
    }
  },
});

// 计算显示的值
const showValue = computed(() => {
  if (state.value) {
    if (typeof state.value === "string") {
      const arr = state.value.split(",");
      return `已选择${size(arr)}个`;
    } else {
      return `已选择${1}个`;
    }
  }
  return props.placeholder;
});

// 计算显示的值
const showValue2 = computed(() => {
  if (state.value) {
    if (typeof state.value === "string") {
      const arr = state.value.split(",");
      const text = arr.map((item) => {
        const findItem = find(props.options, { value: item });
        return findItem?.text;
      });
      return text.join(",");
    } else {
      const findItem = find(props.options, { value: state.value });
      return findItem?.text;
    }
  }
  return "";
});

const [open] = useRuleFormItem<Props, keyof Props, boolean>(props, "open", [
  "update:open",
]);

const methods = {
  ok() {
    console.log(renderValue.value);
    open.value = false;
    emit("ok", unref(state));
  },
};
</script>

<template>
  <view>
    <view v-if="show" class="zk-date-picker-pl" @click="open = true">
      <view class="zk-date-picker-text" v-if="state">
        {{ showValue || state }}
      </view>
      <view v-else> {{ placeholder }}> </view>
    </view>
    <nut-popup :visible="open" position="bottom" safe-area-inset-bottom>
      <view class="zk-date-picker-box">
        <nut-checkbox-group ref="group" v-model="renderValue">
          <nut-cell :key="index" v-for="(item, index) in options">
            <nut-checkbox :label="item.value">
              <view>{{ item.text }}</view>
            </nut-checkbox>
          </nut-cell>
        </nut-checkbox-group>
        <nut-button block @click="methods.ok" type="primary"> 确定 </nut-button>
      </view>
    </nut-popup>
  </view>
</template>

<style scoped lang="scss">
.zk-date-picker {
  &-pl {
    display: flex;
    justify-content: flex-end;
    color: #cccccc;
  }
  &-box {
    padding-top: 40rpx;
    padding-left: 24rpx;
    padding-right: 24rpx;
  }
  &-text {
    color: #333333;
  }
}
</style>
