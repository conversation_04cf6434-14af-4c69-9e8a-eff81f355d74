<script lang="ts">
export default defineComponent({
  name: "zk-step-one",
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: "shared",
  },
});
</script>

<script setup lang="ts">
type Status = "success" | "error" | "wait";
interface Props {
  label: string;
  value: string | number;
  current: string | number;
  des?: string;
  index: number;
  status: Status;
  currentIndex: number;
}
const props = defineProps<Props>();

const isAction = computed(() => {
  return props.currentIndex > props.index;
});
const isCurrent = computed(() => {
  return props.current === props.value;
});

const classname = computed(() => {
  if (isAction.value) {
    return "success";
  }
  if (isCurrent.value) {
    return props.status;
  }
});
</script>

<template>
  <view class="zk-step">
    <view class="zk-step-head">
      <view class="zk-step-line"></view>
      <view class="zk-step-icon">
        <view class="zk-step-icon-inner">
          <view class="zk-step-icon-index def" :class="classname">
            <nut-icon
              size="20rpx"
              v-if="classname === 'success'"
              name="Check"
            ></nut-icon>
            <nut-icon
              v-else-if="classname === 'error'"
              size="18rpx"
              name="close-little"
            ></nut-icon>
          </view>
        </view>
      </view>
    </view>
    <view class="zk-step-main" :class="classname">
      <view>
        {{ label }}
      </view>
      <view class="zk-step-main-des" v-if="des">{{ des }}</view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.zk-step {
  flex: 1;
  display: flex;
  gap: 16rpx;

  &-head {
    position: relative;
    width: 0;
    flex-shrink: 0;
    flex-basis: 50rpx;
  }
  &-line {
    position: absolute;
    top: 5rpx;
    bottom: 0;
    left: 19rpx;
    display: inline-block;
    width: 2rpx;
    background-color: #1677ff;
    z-index: 0;
  }
  &-icon {
    &-inner {
      width: 38rpx;
      height: 38rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    &-index {
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1rpx solid transparent;
      flex-shrink: 0;
      &.def {
        background-color: #fff;
        border: 1rpx solid #1677ff;
        border-radius: 50%;
        width: 36rpx;
        height: 36rpx;
        z-index: 10;
      }
      &.success {
        width: 36rpx;
        height: 36rpx;
        background: #1677ff;
        border: 1rpx solid #1677ff;
        border-radius: 50%;
        position: relative;
        color: #ffffff;
      }
      &.error {
        width: 36rpx;
        height: 36rpx;
        background: #ff3141;
        border: 1rpx solid #ff3141;
        border-radius: 50%;
        position: relative;
        color: #ffffff;
      }
      &.wait {
        background-color: #fff;
        border: 1rpx solid #1677ff;
        border-radius: 50%;
        width: 36rpx;
        height: 36rpx;
      }
    }
  }
  &:last-child {
    .zk-step-line {
      display: none;
    }
  }
  &-main {
    color: #697384;
    font-size: 28rpx;
    font-weight: 400;
    flex-grow: 1;
    padding-bottom: 50rpx;
    display: flex;
    flex-direction: column;
    gap: 8rpx;

    &.success {
      color: #1677ff;
      font-weight: 500;
    }
    &.error {
      color: #ff3141;
      font-weight: 500;
    }

    &.wait {
      color: #1677ff;
      font-weight: 500;
    }
    &-des {
      color: #999999;
      font-size: 24rpx;
      font-weight: 400;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}
</style>
