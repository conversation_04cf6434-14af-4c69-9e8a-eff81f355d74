<script lang="ts">
export default defineComponent({
  name: "zk-steps-one",
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: "shared",
  },
});
</script>

<script setup lang="ts">
import step from "./step-one.vue";
import { useRuleFormItem } from "@/hook/useFormItem";
import { findIndex } from "lodash-es";
import type { CurrentStateStringNumber, ZkStepsOneSchema } from "#/component";
type Status = "success" | "error" | "wait";

interface Props {
  current?: CurrentStateStringNumber;
  options: ZkStepsOneSchema[];
  status?: Status;
}

const props = withDefaults(defineProps<Props>(), {
  current: undefined,
  options: () => [],
  status: "success",
});

const emit = defineEmits(["update:current", "clickStep"]);

const [state] = useRuleFormItem<Props, keyof Props, CurrentStateStringNumber>(
  props,
  "current",
  ["update:current", "clickStep"],
);

const currentIndex = computed(() => {
  return findIndex(props.options, {
    value: props.current,
  });
});
</script>

<template>
  <view class="zk-steps">
    <step
      v-for="(item, index) in options"
      :key="item.value"
      :value="item.value"
      :index="index"
      :label="item.label"
      :des="item.des"
      :current="current"
      :currentIndex="currentIndex"
      :status="status"
    >
    </step>
  </view>
</template>

<style scoped lang="scss">
.zk-steps {
  display: flex;
  flex-direction: column;
}
</style>
