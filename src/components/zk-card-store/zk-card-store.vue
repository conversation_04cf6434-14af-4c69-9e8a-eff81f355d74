<script setup lang="ts">
interface Props {
  url: string;
  name: string;
  address: string;
  phone: string;
}
const emits = defineEmits(["call"]);
const props = defineProps<Props>();
const methods = {
  call() {
    emits("call", props.phone);
  },
};
</script>

<template>
  <view class="zk-card-store">
    <image class="zk-card-store-image" :src="url"> </image>
    <view class="zk-card-store-box">
      <view class="zk-card-store-title">{{ name }}</view>
      <view class="zk-card-store-address">
        {{ address }}
      </view>
    </view>
    <view v-if="phone" @click="methods.call()" class="zk-card-store-call">
      <wd-icon name="call" color="#1677FF" size="38rpx"></wd-icon>
    </view>
  </view>
</template>

<style scoped lang="scss">
.zk-card-store {
  display: flex;
  gap: 35rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  align-items: center;

  &-call {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 65rpx;
    height: 65rpx;
    //background: #f5f5f5;
    border-radius: 50%;
    color: #4e5969;
    border: 1rpx solid #e5e5e5;
  }

  &-image {
    width: 160rpx;
    height: 160rpx;
    border-radius: 16rpx;
  }
  &-box {
    display: flex;
    flex-direction: column;
    height: 90rpx;
    justify-content: space-between;
    flex: 1;
  }
  &-title {
    color: #333;
    font-size: 32rpx;
    font-weight: 500;
  }
  &-address {
    color: #4e5969;
    font-size: 24rpx;
    font-weight: 400;
  }
}
</style>
