<script setup lang="ts">
import { useRuleFormItem } from "@/hook/useFormItem";
type ValueState = boolean;
interface Props {
  open?: ValueState;
  text?: string;
  title?: string;
  control?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  open: false,
  text: "",
  title: "信息",
  control: false,
});
const emit = defineEmits(["update:open", "change", "on-ok", "on-cancel"]);
const [state] = useRuleFormItem<Props, keyof Props, ValueState>(props, "open", [
  "update:open",
  "change",
]);
const methods = {
  onok() {
    !props.control && (state.value = false);
    emit("on-ok");
  },
  cancel() {
    !props.control && (state.value = false);
    emit("on-cancel");
  },
};
</script>

<template>
  <view>
    <nut-popup round :visible="state" position="bottom">
      <view class="zk-affirm-box">
        <view class="zk-affirm-title"> {{ title }} </view>
        <view class="zk-affirm-text">
          {{ text }}
        </view>
        <view v-if="$slots.default" class="zk-affirm-slot">
          <slot></slot>
        </view>
        <view class="zk-affirm-footer">
          <view class="zk-affirm-action">
            <view class="left">
              <nut-button block plain type="primary" @click="methods.cancel()">
                取消
              </nut-button>
            </view>
            <view class="right">
              <nut-button block type="primary" @click="methods.onok()">
                确定
              </nut-button>
            </view>
          </view>
          <nut-safe-area position="bottom" />
        </view>
      </view>
    </nut-popup>
  </view>
</template>

<style scoped lang="scss">
.zk-affirm {
  &-title {
    color: #262626;
    font-size: 36rpx;
    font-weight: 600;
  }
  &-text {
    white-space: pre-wrap;
    font-size: 24rpx;
    color: #333;
    box-sizing: border-box;
    flex: 1;
    height: 0;
    max-height: 900rpx;
    overflow: auto;
    line-height: 45rpx;
  }
  &-slot {
    color: #333;
    box-sizing: border-box;
    flex: 1;
    height: 0;
    max-height: 900rpx;
    overflow: auto;
    line-height: 45rpx;
  }

  &-box {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 600rpx;
    background: linear-gradient(199deg, #e4f0ff 1.63%, #fff 26.94%);
    padding: 40rpx 40rpx 0 40rpx;
    box-sizing: border-box;
    gap: 40rpx;
  }
  &-action {
    display: flex;
    justify-content: space-between;
    gap: 30rpx;
    & .left {
      width: 30%;
    }
    & .right {
      flex-grow: 1;
      width: 0;
    }
  }
}
</style>
