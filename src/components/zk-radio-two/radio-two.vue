<script setup lang="ts">
import { useRuleFormItem } from "@/hook/useFormItem";
type ValueState = string | number;
interface Option {
  label: string;
  value: ValueState;
  des?: string;
}
interface Props {
  value?: ValueState;
  options?: Option[];
}
const props = withDefaults(defineProps<Props>(), {
  value: undefined,
  options: () => [],
});
const emit = defineEmits(["update:value", "change"]);

const [state] = useRuleFormItem<Props, keyof Props, ValueState>(
  props,
  "value",
  ["update:value", "change"],
);
</script>

<template>
  <view class="zk-radio-two">
    <view
      class="zk-radio-two-item"
      v-for="item in options"
      :key="item.value"
      @click="() => (state = item.value)"
    >
      <view
        class="zk-radio-two-item-body def"
        :class="{ action: item.value === state }"
      >
        <view>
          {{ item.label }}
        </view>
        <view
          class="icon"
          :class="{
            'icon-action': item.value === state,
            'icon-def': item.value != state,
          }"
        >
          <nut-icon
            v-if="item.value === state"
            name="checked"
            size="40rpx"
          ></nut-icon>
        </view>
      </view>
      <view class="zk-radio-two-item-des" v-if="item.des">
        {{ item.des }}
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.zk-radio-two {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  padding: 24rpx 0;
  box-sizing: border-box;

  &-item {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    background: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
  }
  &-item-des {
    color: #67737d;
    font-size: 26rpx;
    font-weight: 400;
  }
  &-item-body {
    flex-grow: 1;
    flex-shrink: 0;
    text-align: center;
    font-size: 32rpx;
    display: flex;
    align-items: center;

    justify-content: space-between;
    gap: 16rpx;
    font-weight: 500;

    &.def {
      color: #333;
    }
    &.action {
      color: #333;
    }
    & .icon {
      display: flex;
      align-items: center;
      width: 40rpx;
      height: 40rpx;
      border: 1rpx solid transparent;
      border-radius: 50%;
    }
    & .icon-def {
      background: #fff;
      border: 1rpx solid #cccccc;
    }
    & .icon-action {
      color: #1677ff;
    }
  }
}
</style>
