<script setup lang="ts">
import { useRuleFormItem } from "@/hook/useFormItem";
type ValueState = string | number;
interface Option {
  label: string;
  value: ValueState;
  title?: string;
}
interface Props {
  value?: ValueState;
  options?: Option[];
}
const props = withDefaults(defineProps<Props>(), {
  value: undefined,
  options: () => [],
});
const emit = defineEmits(["update:value", "change"]);

const [state] = useRuleFormItem<Props, keyof Props, ValueState>(
  props,
  "value",
  ["update:value", "change"],
);
</script>

<template>
  <view class="zk-radio-four">
    <view
      class="zk-radio-four-item def"
      v-for="item in options"
      :key="item.value"
      @click="() => (state = item.value)"
      :class="{ action: item.value === state }"
    >
      <view class="title">
        {{ item.title }}
      </view>
      <view class="label">
        {{ item.label }}
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.zk-radio-four {
  display: flex;
  justify-content: space-between;
  gap: 40rpx;
  &-item {
    flex-grow: 1;
    width: 0;
    height: 144rpx;
    flex-shrink: 0;
    border-radius: 16rpx;
    font-size: 26rpx;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    &.def {
      color: #999;
      background: #f9fcfe;
      border: 1px solid rgba(22, 119, 255, 0.2);
      .title {
        color: #333;
        font-size: 40rpx;
        line-height: 40rpx;
        font-weight: bold; /* 设置字体加粗 */
        &:first-letter {
          font-size: 28rpx;
          margin-right: 4rpx;
        }
      }
    }
    &.action {
      border: 1px solid #1677ff;
      background: #1677ff;
      color: #fff;
      position: relative;
      .title {
        color: #fff;
      }
      &:after {
        content: "";
        position: absolute;
        bottom: -10rpx;
        left: 50%;
        width: 20rpx;
        height: 20rpx;
        background: #1677ff;
        transform: translateX(-50%) rotate(45deg);
      }
    }
  }
}
</style>
