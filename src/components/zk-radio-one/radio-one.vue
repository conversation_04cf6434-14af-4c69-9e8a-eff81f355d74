<script setup lang="ts">
import { useRuleFormItem } from "@/hook/useFormItem";
type ValueState = string | number;
interface Option {
  label: string;
  value: ValueState;
}
interface Props {
  value?: ValueState;
  options?: Option[];
}
const props = withDefaults(defineProps<Props>(), {
  value: undefined,
  options: () => [],
});
const emit = defineEmits(["update:value", "change"]);

const [state] = useRuleFormItem<Props, keyof Props, ValueState>(
  props,
  "value",
  ["update:value", "change"],
);
</script>

<template>
  <view class="zk-radio-one">
    <view
      class="zk-radio-one-item common-tr def"
      :class="{ action: item.value === state }"
      v-for="item in options"
      :key="item.value"
      @click="() => (state = item.value)"
    >
      <view
        class="icon"
        :class="{
          'icon-action': item.value === state,
          'icon-def': item.value !== state,
        }"
      >
        <nut-icon
          v-if="item.value === state"
          name="checked"
          size="40rpx"
        ></nut-icon>
      </view>
      <view>
        {{ item.label }}
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.zk-radio-one {
  display: flex;
  flex-direction: column;
  gap: 40rpx;
  padding: 20rpx 36rpx;
  &-item {
    flex-grow: 1;
    height: 120rpx;
    flex-shrink: 0;
    border-radius: 100rpx;
    font-size: 36rpx;
    text-align: center;
    display: flex;
    align-items: center;
    padding: 0 40rpx;
    justify-content: center;
    gap: 16rpx;
    border: transparent 1rpx solid;

    &.def {
      color: #333;
      background: #f8fafe;
      z-index: 1;
    }
    &.action {
      background: #f8fafe;
      color: #333;
      border-color: #1677ff;
      z-index: 1;
    }
    & .icon {
      display: flex;
      align-items: center;
      width: 40rpx;
      height: 40rpx;
      border: 1rpx solid transparent;
      border-radius: 50%;
    }
    & .icon-def {
      background: #fff;
      border: 1rpx solid #cccccc;
      z-index: 1;
    }
    & .icon-action {
      color: #1677ff;
      z-index: 1;
    }
  }
}
</style>
