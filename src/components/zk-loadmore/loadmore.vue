<script setup lang="ts">
import type { ScrollViewStatusType } from "@/hook/useScrollView";
interface Props {
  status: ScrollViewStatusType;
}
defineProps<Props>();
</script>

<template>
  <view class="zk-loadmore">
    <wd-text v-if="status === 'loadmore'" text="下滑加载更多"> </wd-text>
    <wd-text v-else-if="status === 'loading'" text="加载中..."> </wd-text>
    <wd-text v-else-if="status === 'nomore'" text="没有更多了"> </wd-text>
    <wd-text v-else-if="status === 'error'" text="加载失败"></wd-text>
  </view>
</template>

<style scoped lang="scss">
.zk-loadmore {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50rpx;
}
</style>
