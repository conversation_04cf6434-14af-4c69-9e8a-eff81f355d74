<script setup lang="ts">
import { useRuleFormItem } from "@/hook/useFormItem";
defineComponent({ name: "zk-category" });

type ValueState = string | number;
interface Option {
  label: string;
  value: ValueState;
}
interface Props {
  value?: ValueState;
  options?: Option[];
}
const props = withDefaults(defineProps<Props>(), {
  value: undefined,
  options: () => [],
});
const emit = defineEmits(["update:value", "change"]);

const [state] = useRuleFormItem<Props, keyof Props, ValueState>(
  props,
  "value",
  ["update:value", "change"],
);
</script>

<template>
  <view class="zk-category">
    <view
      class="zk-category-item common-tr def"
      v-for="item in options"
      :key="item.value"
      :class="{
        action: item.value === state,
      }"
      @click="() => (state = item.value)"
    >
      {{ item.label }}
    </view>
  </view>
</template>

<style scoped lang="scss">
.zk-category {
  display: flex;
  background: #fff;
  padding: 14rpx 24rpx;
  gap: 16rpx;
  overflow-y: auto;

  &-item {
    height: 60rpx;
    flex-shrink: 0;
    border-radius: 31rpx;
    padding: 0 33rpx;
    font-size: 26rpx;
    line-height: 60rpx;
    text-align: center;
    &.def {
      color: #333;
      background: #f9f9f9;
    }
    &.action {
      background: #e9f0fe;
      color: #1677ff;
    }
  }
}
</style>
