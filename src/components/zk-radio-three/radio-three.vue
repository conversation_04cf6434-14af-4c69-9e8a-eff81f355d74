<script setup lang="ts">
import { useRuleFormItem } from "@/hook/useFormItem";
type ValueState = string | number;
interface Option {
  label: string;
  value: ValueState;
}
interface Props {
  value?: ValueState;
  options?: Option[];
}
const props = withDefaults(defineProps<Props>(), {
  value: undefined,
  options: () => [],
});
const emit = defineEmits(["update:value", "change"]);

const [state] = useRuleFormItem<Props, keyof Props, ValueState>(
  props,
  "value",
  ["update:value", "change"],
);
</script>

<template>
  <view class="zk-three">
    <view
      class="zk-three-item common-tr def"
      v-for="item in options"
      :key="item.value"
      :class="{
        action: item.value === state,
      }"
      @click="() => (state = item.value)"
    >
      {{ item.label }}
    </view>
  </view>
</template>

<style scoped lang="scss">
.zk-three {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  padding: 10rpx 0;

  &-item {
    height: 87rpx;
    flex-shrink: 0;
    border-radius: 10rpx;
    padding: 0 24rpx;
    font-size: 28rpx;
    line-height: 87rpx;
    text-align: left;
    border: 1rpx solid transparent;

    &.def {
      color: #333;
      border: 1rpx solid #eaf5ff;
      background: #f6fbff;
    }
    &.action {
      color: #fff;
      border: 1rpx solid #eaf5ff;
      background: #1677ff;
    }
  }
}
</style>
