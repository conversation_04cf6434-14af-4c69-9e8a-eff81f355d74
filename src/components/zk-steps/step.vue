<script lang="ts">
export default defineComponent({
  name: "zk-step",
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: "shared",
  },
});
</script>

<script setup lang="ts">
interface Props {
  label: string;
  value: string | number;
  current: string | number;
  des?: string;
  index: number;
}
const props = defineProps<Props>();

const isCurrent = computed(() => {
  return props.current === props.value;
});
</script>

<template>
  <view class="zk-step">
    <view class="zk-step-head">
      <view class="zk-step-line"></view>
      <view class="zk-step-icon">
        <view class="zk-step-icon-inner">
          <view v-if="isCurrent" class="zk-step-icon-index action"> </view>
          <view v-else class="zk-step-icon-index def">
            {{ index + 1 }}
          </view>
        </view>
      </view>
    </view>
    <view
      class="zk-step-main"
      :class="{
        action: isCurrent,
      }"
    >
      {{ label }}
    </view>
  </view>
</template>

<style scoped lang="scss">
.zk-step {
  flex: 1;
  text-align: center;

  &-head {
    position: relative;
    margin-bottom: 10rpx;
  }
  &-line {
    position: absolute;
    top: 14rpx;
    right: -85%;
    left: 85%;
    width: 30%;
    display: inline-block;
    height: 8rpx;
    border-radius: 10rpx;
    background-color: #d3e5f8;
  }
  &-icon {
    display: flex;
    justify-content: center;
    align-items: center;

    &-inner {
      width: 36rpx;
      height: 36rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    &-index {
      display: flex;
      justify-content: center;
      align-items: center;
      &.def {
        background-color: #7a8aa2;
        color: #ffffff;
        font-size: 24rpx;
        line-height: 36rpx;
        border-radius: 50%;
        text-align: center;
        width: 36rpx;
        height: 36rpx;
      }
      &.action {
        width: 28rpx;
        height: 28rpx;
        background: #1677ff;
        border: 4rpx solid #d3e5f8;
        border-radius: 50%;
      }
    }
  }
  &:last-child {
    .zk-step-line {
      display: none;
    }
  }
  &-main {
    color: #697384;
    text-align: center;
    font-size: 28rpx;
    font-weight: 400;
    &.action {
      color: #333;
      font-weight: 500;
    }
  }
}
</style>
