<script lang="ts">
export default defineComponent({
  name: "zk-steps",
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: "shared",
  },
});
</script>

<script setup lang="ts">
import step from "./step.vue";
import { useRuleFormItem } from "@/hook/useFormItem";

type CurrentState = string | number;
type Status = "success" | "error" | "warning";
interface Option {
  label: string;
  value: CurrentState;
  des?: string;
}
interface Props {
  current?: CurrentState;
  options?: Option[];
  status?: Status;
}
const props = withDefaults(defineProps<Props>(), {
  current: undefined,
  options: () => [],
  status: "success",
});

const emit = defineEmits(["update:current", "clickStep"]);

const [state] = useRuleFormItem<Props, keyof Props, CurrentState>(
  props,
  "current",
  ["update:current", "clickStep"],
);
</script>

<template>
  <view class="zk-steps">
    <step
      v-for="(item, index) in options"
      :key="item.value"
      :value="item.value"
      :index="index"
      :label="item.label"
      :current="current"
    >
    </step>
  </view>
</template>

<style scoped lang="scss">
.zk-steps {
  display: flex;
}
</style>
