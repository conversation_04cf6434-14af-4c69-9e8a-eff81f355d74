<script setup lang="ts">
import { find } from "lodash-es";

type CurrentState = string | number;
interface buttonExtend {
  type?: string;
  text: string;
}
interface option {
  value: CurrentState;
  ok: (...any: any[]) => void;
  cancel: (...any: any[]) => void;
  okExtend: buttonExtend;
  cancelExtend: buttonExtend;
  cancelShow: boolean;
  okShow: boolean;
}
interface Props {
  options: option[];
  current?: CurrentState;
  loading: boolean;
  safe: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  current: undefined,
  options: () => [],
  safe: true,
  loading: false,
});

const emit = defineEmits(["defaultOk", "defaultCancel"]);

const defaultOption = {
  value: 0,
  ok: () => {
    emit("defaultOk");
  },
  cancel: () => {
    emit("defaultCancel");
  },
  okExtend: {
    type: "primary",
    text: "确定",
    show: true,
  },
  cancelExtend: {
    type: "default",
    text: "取消",
    show: true,
  },
  okShow: true,
  cancelShow: true,
};

// 通过 current 计算第几个 option
const currentOpt = computed<option>(() => {
  return find(props.options, { value: props.current }) || defaultOption;
});

// 默认值是否显示取消
const cancelShow = computed(() => {
  if (unref(currentOpt).cancelShow === undefined) {
    return true;
  }
  return unref(currentOpt).cancelShow;
});

// 默认显示确认
const okShow = computed(() => {
  if (unref(currentOpt).okShow === undefined) {
    return true;
  }
  return unref(currentOpt).okShow;
});

const methods = {
  onOk() {
    currentOpt.value.ok();
  },
  onCancel() {
    currentOpt.value.cancel();
  },
};
</script>

<template>
  <view class="zk-button-action">
    <view class="zk-button-action-box">
      <view class="zk-button-action-item" v-if="cancelShow">
        <nut-button
          @click="methods.onCancel()"
          block
          v-bind="currentOpt.cancelExtend"
        >
          {{ currentOpt.cancelExtend.text }}
        </nut-button>
      </view>
      <view class="zk-button-action-item" v-if="okShow">
        <nut-button
          :loading="loading"
          @click="methods.onOk()"
          block
          v-bind="currentOpt.okExtend"
        >
          {{ currentOpt.okExtend.text }}
        </nut-button>
      </view>
    </view>
    <nut-safe-area v-if="safe" position="bottom"></nut-safe-area>
  </view>
</template>

<style scoped lang="scss">
.zk-button-action {
  &-box {
    display: flex;
    gap: 24rpx;
  }
  &-item {
    flex: 1;
  }
}
</style>
