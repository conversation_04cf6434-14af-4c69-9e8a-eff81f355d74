<script setup lang="ts">
interface Props {
  icon?: string;
  title?: string;
  head?: boolean;
  footer?: string;
  // 高度
  height?: string;
}
withDefaults(defineProps<Props>(), {
  head: true,
  height: "auto",
});
</script>

<template>
  <view
    :style="{
      height,
    }"
    class="zk-card"
  >
    <view class="zk-card-heard" v-if="head">
      <view v-if="$slots.icon">
        <slot name="icon"> </slot>
      </view>
      <image class="icon" v-else-if="icon" :src="icon"> </image>
      <view class="title" v-if="$slots.title">
        <slot name="title"> </slot>
      </view>
      <view class="title" v-else>{{ title }}</view>
      <view class="extra" v-if="$slots.extra">
        <slot name="extra"> </slot>
      </view>
    </view>
    <view class="zk-card-body">
      <slot></slot>
    </view>
    <view v-if="$slots.footer">
      <slot name="footer"> </slot>
    </view>
    <view class="zk-card-footer" v-else-if="footer">
      {{ footer }}
    </view>
  </view>
</template>

<style scoped lang="scss">
.zk-card {
  display: flex;
  box-sizing: border-box;
  flex-direction: column;
  padding: 0 24rpx;
  background: #fff;
  border-radius: 16rpx;
  &-heard {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 0;
    flex-basis: 94rpx;
    flex-shrink: 0;
    gap: 12rpx;
    border-bottom: #eee 1rpx solid;

    & .title {
      flex-grow: 1;
      width: 0;
      display: flex;
      align-items: center;
      color: #333;
      font-size: 28rpx;
      font-weight: 500;
    }

    & .extra {
      display: flex;
      align-items: center;
    }

    & .icon {
      width: 52rpx;
      height: 52rpx;
    }
  }
  &-body {
    padding: 24rpx 0;
    box-sizing: border-box;
  }
  &-footer {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 24rpx 0;
    color: #333;
    font-size: 24rpx;
    font-weight: 400;
  }
}
</style>
