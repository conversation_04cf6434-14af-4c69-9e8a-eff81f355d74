<script setup lang="ts">
import { useRuleFormItem } from "@/hook/useFormItem";
type ValueState = boolean;
interface Props {
  loading?: ValueState;
  top?: number | string;
  // 垂直居中
  center?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  loading: false,
  top: 400,
  center: false,
});

const emit = defineEmits(["update:loading", "change"]);
const [loading] = useRuleFormItem<Props, keyof Props, ValueState>(
  props,
  "loading",
  ["update:loading", "change"],
);
// loading 过程
type loadingProcess = "show" | "show-hidden" | "hidden";
// 定时器种子
let timer = ref<NodeJS.Timeout | null>(null);

const showClass = computed<loadingProcess>(() => {
  if (timer.value && !loading.value) {
    return "show-hidden";
  } else if (loading.value) {
    return "show";
  } else {
    return "hidden";
  }
});

watch(loading, (v) => {
  console.log("watch", v);
  if (v) {
    if (timer) {
      timer.value && clearTimeout(timer.value);
      timer.value = null;
    }
  } else {
    if (!timer.value) {
      timer.value = setTimeout(() => {
        loading.value = false;
        timer.value = null;

        console.log("setTime", timer, loading.value);
      }, 200);
    }
  }
});
const posStyle = computed(() => {
  if (props.center) {
    return {
      top: `45%`,
    };
  }
  return {
    top: `${props.top}rpx`,
  };
});
</script>

<template>
  <view class="zk-loading" :class="showClass">
    <view class="zk-loading-pos">
      <view class="zk-loading-loader" :style="posStyle"> </view>
    </view>
    <view class="zk-loading-mask"></view>
  </view>
</template>

<style scoped lang="scss">
.zk-loading {
  position: absolute;
  top: 0;
  left: 0;

  &.show-hidden {
    z-index: 999;
    visibility: visible;
    width: 100%;
    height: 100%;
    .zk-loading {
      &-mask {
        transition:
          background 0.2s,
          backdrop-filter 0.2s;
        background: transparent;
        backdrop-filter: none;
      }
      &-loader {
        display: grid;
      }
    }
  }

  &.hidden {
    z-index: -100;
    visibility: hidden;
    width: 0;
    height: 0;

    .zk-loading {
      &-mask {
        transition:
          background 0.2s,
          backdrop-filter 0.2s;
        background: none;
        backdrop-filter: none;
      }
      &-loader {
        display: none;
      }
    }
  }
  &.show {
    z-index: 999;
    visibility: visible;
    width: 100%;
    height: 100%;
    .zk-loading {
      &-mask {
        transition:
          background 0.2s,
          backdrop-filter 0.2s;
        background: rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(10rpx);
      }
      &-loader {
        display: grid;
      }
    }
  }

  &-pos {
    display: flex;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 20;
  }

  &-mask {
    position: absolute;
    //background: rgba(255, 255, 255, 0.3);
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    // 模糊
    //backdrop-filter: blur(10rpx);
  }
  &-loader {
    position: absolute;
    width: 50rpx;
    aspect-ratio: 1;
    display: grid;
    &:before,
    &:after {
      content: "";
      grid-area: 1/1;
      --c: no-repeat radial-gradient(farthest-side, #1677ff 92%, #0000);
      background:
        var(--c) 50% 0,
        var(--c) 50% 100%,
        var(--c) 100% 50%,
        var(--c) 0 50%;
      background-size: 12rpx 12rpx;
      animation: l12 1s infinite;
    }
    &:before {
      margin: 4rpx;
      filter: hue-rotate(45deg);
      background-size: 8rpx 8rpx;
      animation-timing-function: linear;
    }
  }
}

@keyframes l12 {
  100% {
    transform: rotate(0.5turn);
  }
}
</style>
