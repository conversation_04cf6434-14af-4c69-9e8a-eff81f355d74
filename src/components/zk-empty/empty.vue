<script setup lang="ts">
type StatusType = "success" | "fail";
interface Props {
  status?: StatusType;
  des?: string;
  title?: string;
}
const props = withDefaults(defineProps<Props>(), {
  status: "success",
  des: "",
  title: "",
});

// 状态 map
const statusMap = {
  success: {
    title: "成功",
    des: "2",
    image:
      "https://tzcjlw.oss-cn-shanghai.aliyuncs.com/mini-program/empty/Group%20427322684.png",
  },
  fail: {
    title: "失败",
    des: "3",
    image:
      "https://tzcjlw.oss-cn-shanghai.aliyuncs.com/mini-program/empty/Group%20427322498.png",
  },
};

// 计算属性返回
const currentStatus = computed(
  () => statusMap[props.status] || statusMap.success,
);
</script>

<template>
  <view class="empty">
    <image
      class="empty-image"
      mode="scaleToFill"
      :src="currentStatus.image"
    ></image>
    <view class="empty-title">
      {{ title || currentStatus.title }}
    </view>
    <view class="empty-des">
      {{ des || currentStatus.des }}
    </view>
  </view>
</template>

<style scoped lang="scss">
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20rpx;

  &-image {
    width: 330rpx;
    height: 230rpx;
  }

  &-title {
    margin-top: 18rpx;
    color: #000;
    font-size: 32rpx;
    font-weight: 500;
    height: 45rpx;
    line-height: 45rpx;
  }
  &-des {
    margin-top: 8rpx;
    color: #999;
    font-size: 28rpx;
    font-weight: 400;
    height: 39rpx;
    line-height: 39rpx;
  }
}
</style>
