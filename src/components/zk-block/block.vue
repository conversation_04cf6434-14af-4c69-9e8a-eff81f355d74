<script setup lang="ts">
interface Props {
  openType?: string;
}
defineProps<Props>();
const emit = defineEmits(["chooseavatar"]);
</script>

<template>
  <button
    class="zk-block"
    :open-type="openType"
    @chooseavatar="emit('chooseavatar', $event)"
  >
    <slot></slot>
  </button>
</template>

<style scoped lang="scss">
.zk-block {
  padding: 0;
  display: inline-flex;
  margin: 0;
  text-align: left;
  outline: none;
  line-height: normal;
  border-radius: 0;
  background-color: transparent;

  &:after {
    border: none;
  }
}
</style>
