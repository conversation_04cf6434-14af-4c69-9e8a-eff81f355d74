<script setup lang="ts">
interface Props {
  icon?: string;
  title?: string;
}
defineProps<Props>();
</script>

<template>
  <view class="zk-card-info">
    <view class="zk-card-info-heard">
      <view v-if="$slots.icon">
        <slot name="icon"> </slot>
      </view>
      <image class="icon" v-else-if="icon" :src="icon"> </image>
      <view class="title" v-if="$slots.title">
        <slot name="title"> </slot>
      </view>
      <view class="title" v-else>{{ title }}</view>
      <view class="extra" v-if="$slots.extra">
        <slot name="extra"> </slot>
      </view>
    </view>
    <view class="zk-card-info-body">
      <slot></slot>
    </view>
    <view class="zk-card-info-footer" v-if="$slots.footer">
      <slot name="footer"> </slot>
    </view>
  </view>
</template>

<style scoped lang="scss">
.zk-card-info {
  display: flex;
  box-sizing: border-box;
  flex-direction: column;
  background: #fff;
  border-radius: 16rpx;
  padding: 0 12rpx;
  &-heard {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
    gap: 12rpx;
    padding: 24rpx 12rpx 24rpx 24rpx;

    & .title {
      flex-grow: 1;
      width: 0;
      display: flex;
      align-items: center;
      color: #333;
      font-size: 28rpx;
      font-weight: 500;
    }

    & .extra {
      display: flex;
      align-items: center;
    }

    & .icon {
      width: 52rpx;
      height: 52rpx;
    }
  }
  &-body {
    padding: 0 12rpx 24rpx 12rpx;
    box-sizing: border-box;
  }
  &-footer {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 24rpx 12rpx;
    color: #333;
    font-size: 24rpx;
    font-weight: 400;
  }
}
</style>
