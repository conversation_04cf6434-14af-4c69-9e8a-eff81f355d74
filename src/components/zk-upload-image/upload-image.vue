<script setup lang="ts">
import { get, map, size } from "lodash-es";
import { apiUpload } from "@/api/file";
import { useRuleFormItem } from "@/hook/useFormItem";
type ValueState = string;

interface Props {
  title?: string;
  value?: ValueState;
  count?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "请上传图片",
  count: 1,
});
const emit = defineEmits(["update:value", "change"]);
const [state] = useRuleFormItem<Props, keyof Props, ValueState>(
  props,
  "value",
  ["update:value", "change"],
);

const loading = ref(false);
const showPreview = ref(false);
const initno = ref(0);

const imageList = computed(() => {
  if (!state.value) {
    return [];
  }

  const urls = state.value.split(",");
  return map(urls, (item) => {
    return {
      src: item,
    };
  });
});

const methods = {
  // 点击图片上传
  onClick() {
    uni.chooseMedia({
      mediaType: ["image"],
      sourceType: ["camera", "album"],
      count: 1,
      success: (res) => {
        // 获取图片
        const tempFilePath = get(res, "tempFiles.0.tempFilePath");
        console.log("path", tempFilePath);
        loading.value = true;
        apiUpload(tempFilePath)
          .then((res) => {
            const imgurl = `${get(res, "host")}${get(res, "url")}`;
            if (!imgurl) {
              return uni.showToast({
                title: "上传失败",
                icon: "none",
              });
            }
            const urlList = imageList.value.map((item) => item.src);
            urlList.push(imgurl);
            state.value = urlList.join(",");
          })
          .catch(() => {
            uni.showToast({
              title: "上传失败",
              icon: "none",
            });
          })
          .finally(() => {
            loading.value = false;
          });
      },
    });
  },
  // 删除
  onDelete(index: number) {
    const urlList = imageList.value.map((item) => item.src);
    urlList.splice(index, 1);
    state.value = urlList.join(",");
  },
  hideFn() {
    showPreview.value = false;
  },
  prw(index: number) {
    initno.value = index;
    showPreview.value = true;
  },
};
</script>

<template>
  <view class="zk-upload-image">
    <view
      class="zk-upload-image-upload"
      @click="methods.onClick()"
      v-if="size(imageList) < count"
    >
      <image
        class="zk-upload-image-bg"
        src="https://tzcjlw.oss-cn-shanghai.aliyuncs.com/mini-program/upload/line.png"
      ></image>
      <view class="zk-upload-image-loading" v-if="loading">
        <nut-icon name="loading" size="40rpx"></nut-icon>
      </view>
      <view class="zk-upload-image-box">
        <image
          class="zk-upload-image-icon"
          src="https://tzcjlw.oss-cn-shanghai.aliyuncs.com/mini-program/upload/camera.png"
        ></image>
        <view class="zk-upload-image-text">
          {{ title }}
        </view>
      </view>
    </view>
    <view
      class="zk-upload-image-prw"
      v-for="(item, index) in imageList"
      :key="index"
    >
      <view class="zk-upload-image-prw image" @click="methods.prw(index)">
        <image
          :src="item.src"
          mode="aspectFill"
          class="zk-upload-image-prw image"
        >
        </image>
      </view>

      <view class="button-close" @click="methods.onDelete(index)">
        <nut-icon size="18rpx" name="close-little"></nut-icon>
      </view>
    </view>
    <nut-image-preview
      :show="showPreview"
      :images="imageList"
      @close="methods.hideFn"
      :init-no="initno"
      :autoplay="0"
    />
  </view>
</template>

<style scoped lang="scss">
.zk-upload-image {
  display: flex;
  margin-top: 24rpx;
  &-prw {
    background: #f5f5f5;
    position: relative;
    overflow: visible; // 确保子元素不会被裁剪
    &.image {
      width: 315rpx;
      height: 200rpx;
      border-radius: 14rpx;
      overflow: hidden;
    }
    .button-close {
      position: absolute;
      top: -15rpx;
      right: -15rpx;
      width: 40rpx;
      height: 40rpx;
      background: #ffffff;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10;
      border: 1rpx #eeeeee solid;
      color: red;
    }
  }
  &-upload {
    width: 315rpx;
    height: 200rpx;
    background: #f5f5f5;
    border: 1rpx #eeeeee dashed;
    border-radius: 14rpx;
    display: flex;
    position: relative;
    overflow: hidden;
  }
  &-loading {
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
  }
  &-bg {
    position: absolute;
    width: 252rpx;
    height: 83rpx;
    flex-shrink: 0;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  &-icon {
    width: 70rpx;
    height: 57.4rpx;
  }
  &-box {
    width: 100%;
    z-index: 9;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 14rpx;
  }
  &-text {
    color: #4f80f9;
    text-align: center;
    font-size: 24rpx;
    letter-spacing: 1rpx;
  }
}
</style>
