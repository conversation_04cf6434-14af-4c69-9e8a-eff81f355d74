<script setup lang="ts">
import tinycolor from "tinycolor2";

const colorMap = {
  primary: "#1677FF",
  success: "#22B35E",
  warning: "#FF9F18",
  danger: "#FF3141",
  info: "#8a6de9",
};
type ValueState = keyof typeof colorMap;
interface Props {
  type?: ValueState;
  customColor?: string;
}
const props = withDefaults(defineProps<Props>(), {
  type: "primary",
  customColor: "",
});

const color = computed(() => {
  if (props.customColor) {
    return props.customColor;
  }
  return colorMap[props.type] || colorMap.success;
});
// 计算背景在 color 上计算
const backgroundColor = computed(() => {
  const baseColor = color.value; // 基础颜色
  // return tinycolor(baseColor).lighten(35).toString();
  // 与白色混合，50% 混合度
  return tinycolor.mix(baseColor, "#FFFFFF", 85).toString();
});
</script>

<template>
  <view
    class="zk-tag"
    :style="{
      color,
      backgroundColor,
    }"
  >
    <slot></slot>
  </view>
</template>

<style scoped lang="scss">
.zk-tag {
  display: inline-block;
  padding: 0 10rpx;
  height: 44rpx;
  line-height: 44rpx;
  font-size: 24rpx;
  border-radius: 6rpx;
  // 字体间隔
  letter-spacing: 2rpx;
}
</style>
