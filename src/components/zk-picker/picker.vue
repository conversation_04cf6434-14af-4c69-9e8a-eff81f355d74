<script setup lang="ts">
import { useRuleFormItem } from "@/hook/useFormItem";
import { find, first, size } from "lodash-es";

type ValueState = string | number;

interface Option {
  text: string;
  value: string;
}

interface Props {
  open?: boolean;
  value?: ValueState;
  options: Option[];
  // value 格式化
  placeholder?: string;
  show?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  open: false,
  placeholder: "选择",
  options: () => [],
  show: true,
});
const emit = defineEmits(["update:value", "change", "update:open", "ok"]);
const [state] = useRuleFormItem<Props, keyof Props, ValueState>(
  props,
  "value",
  ["update:value", "change"],
);

// 参数中转
const renderValue = computed({
  get() {
    if (state.value) {
      return [state.value];
    } else {
      return [];
    }
  },
  set(val: any) {
    if (size(val)) {
      state.value = first(val);
    } else {
      state.value = "";
    }
  },
});

// 计算显示的值
const showValue = computed(() => {
  return find(props.options, { value: state.value })?.text;
});

const [open] = useRuleFormItem<Props, keyof Props, boolean>(props, "open", [
  "update:open",
]);

const methods = {
  ok() {
    // 判断有没有选择值
    if (!state.value && size(props.options)) {
      state.value = first(props.options)?.value as string;
    }
    open.value = false;
    emit("ok", unref(state));
  },
};
</script>

<template>
  <view>
    <view v-if="show" class="zk-date-picker-pl" @click="open = true">
      <view class="zk-date-picker-text" v-if="state">
        {{ showValue || state }}
      </view>
      <view v-else> {{ placeholder }}> </view>
    </view>
    <nut-popup :visible="open" position="bottom" safe-area-inset-bottom>
      <view class="zk-date-picker-box">
        <nut-picker
          v-model:model-value="renderValue"
          :columns="options"
          title="城市选择"
          :show-toolbar="false"
        >
          <nut-button block @click="methods.ok" type="primary">
            确定
          </nut-button>
        </nut-picker>
      </view>
    </nut-popup>
  </view>
</template>

<style scoped lang="scss">
.zk-date-picker {
  &-pl {
    display: flex;
    justify-content: flex-end;
    color: #cccccc;
  }
  &-box {
    padding-top: 40rpx;
    padding-left: 24rpx;
    padding-right: 24rpx;
  }
  &-text {
    color: #333333;
  }
}
</style>
