<script lang="ts">
	export default defineComponent({
		name: 'zk-text',
		options: {
			virtualHost: true,
			addGlobalClass: true,
			styleIsolation: 'shared',
		},
	})
</script>
<script setup lang="ts">
	import type { CSSProperties } from 'vue'
	interface Props {
		color?: string
		size?: string | number
		// 单位
		unit?: string
		text?: string
		bold?: boolean
		// 布局
		align?: CSSProperties['textAlign']
	}
	const props = withDefaults(defineProps<Props>(), {
		color: '#333',
		size: '24',
		unit: 'rpx',
		text: '',
		bold: false,
		align: 'left',
	})
	const emit = defineEmits(['click'])
	const style = computed<CSSProperties>(() => {
		return {
			color: props.color,
			fontSize: `${props.size}${props.unit}`,
			fontWeight: props.bold ? 'bold' : 'normal',
			textAlign: props.align,
		}
	})
</script>

<template>
	<text @click="emit('click')" class="zk-text" :style="style">
		<text v-if="text">{{ text }}</text>
		<slot></slot>
	</text>
</template>

<style scoped lang="scss">
	.zk-text {
		// 换行、
		//white-space: pre-wrap;
		word-break: break-all;
	}
</style>
