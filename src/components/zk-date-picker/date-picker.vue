<script setup lang="ts">
import { useRuleFormItem } from "@/hook/useFormItem";
import type { datepickerType } from "nutui-uniapp";
import dayjs from "dayjs";
type ValueState = string;

type DatepickerType = (typeof datepickerType)[number];
interface Props {
  open?: boolean;
  value?: ValueState;
  minDate?: Date;
  maxDate?: Date;
  type?: DatepickerType;
  // value 格式化
  valueFormat?: string;
  placeholder?: string;
}
const props = withDefaults(defineProps<Props>(), {
  type: "datetime",
  valueFormat: "YYYY-MM-DD HH:mm:ss",
  open: false,
  placeholder: "选择",
  minDate: () => {
    return dayjs("1900-1-1").toDate();
  },
});
const emit = defineEmits(["update:value", "change", "update:open"]);
const [state] = useRuleFormItem<Props, keyof Props, ValueState>(
  props,
  "value",
  ["update:value", "change"],
);

const [open] = useRuleFormItem<Props, keyof Props, boolean>(props, "open", [
  "update:open",
]);

const interValue = ref();

const renderValue = computed({
  get() {
    if (!unref(interValue)) {
      return undefined;
    }
    return dayjs(unref(interValue)).format(props.valueFormat);
  },
  set(val) {
    if (!val) {
      interValue.value = undefined;
    }
    interValue.value = dayjs(val).toDate();
  },
});

watch(renderValue, (val) => {
  // 内部值变化对比和 state 是否有变化
  if (val !== unref(state) && val) {
    state.value = val;
  }
});
watch(state, (val) => {
  if (val !== unref(renderValue) && val) {
    renderValue.value = val;
  }
});

const methods = {
  ok() {
    if (!state.value) {
      // 设置当前的时间
      state.value = dayjs().format(props.valueFormat);
    }
    open.value = false;
  },
};
</script>

<template>
  <view>
    <view class="zk-date-picker-pl" @click="open = true">
      <view class="zk-date-picker-text" v-if="state"> {{ state }}</view>
      <view v-else> {{ placeholder }}> </view>
    </view>
    <nut-popup :visible="open" position="bottom" safe-area-inset-bottom>
      <view class="zk-date-picker-box">
        <nut-date-picker
          v-model="interValue"
          is-show-chinese
          :show-toolbar="false"
          :type="type"
          :minDate="minDate"
        >
          <nut-button block @click="methods.ok" type="primary">
            确定
          </nut-button>
        </nut-date-picker>
      </view>
    </nut-popup>
  </view>
</template>

<style scoped lang="scss">
.zk-date-picker {
  &-pl {
    display: flex;
    justify-content: flex-end;
    color: #cccccc;
  }
  &-box {
    padding-top: 40rpx;
    padding-left: 24rpx;
    padding-right: 24rpx;
  }
  &-text {
    color: #333333;
  }
}
</style>
