<script setup lang="ts">
import { h } from "vue";
import type { ZkDesSchema } from "#/component";
import { get } from "lodash-es";

interface Props {
  // 映射
  schema: ZkDesSchema;
  data?: Recordable;
}
const props = withDefaults(defineProps<Props>(), {
  data: () => ({}),
});
const render = computed(() => {
  if (!props.schema.render) {
    return;
  }
  return (
    props.schema.render(get(props.data, props.schema.field), props.data) || "--"
  );
});
// 是否显示渲染组件
const isRender = computed(() => {
  if (!props.schema.render) {
    return false;
  }
  return true;
});
</script>

<template>
  <view class="zk-des-item">
    <view class="zk-des-item-label">
      {{ schema.label }}
    </view>
    <view v-if="isRender" class="zk-des-item-render">
      {{ render }}
    </view>
    <view v-else class="zk-des-item-render">
      {{ get(data, schema.field, "--") }}
    </view>
  </view>
</template>

<style scoped lang="scss">
.zk-des-item {
  display: flex;
  align-items: center;
  justify-content: space-between;

  &-label {
    color: #666;
    font-size: 26rpx;
    font-weight: 400;
    min-width: 144rpx;
  }
  &-render {
    flex-grow: 1;
    width: 0;
    color: #333;
    font-size: 26rpx;
    font-weight: 400;
  }
}
</style>
