<script setup lang="ts">
import type { ZkDesSchema } from "#/component";
import desItem from "./des-item.vue";
import { filter, get } from "lodash-es";
interface Props {
  // 映射
  schemas?: ZkDesSchema[];
  data?: Recordable;
}
const props = withDefaults(defineProps<Props>(), {
  schemas: () => [],
  data: () => ({}),
});

const schemas = computed(() => {
  return filter(props.schemas, (item) => {
    // 判断 show 是函数还是布尔值
    if (typeof item.show === "function") {
      return item.show(get(props.data, item.field), props.data);
    } else if (typeof item.show === "boolean") {
      return item.show;
    }
    return true;
  });
});
</script>

<template>
  <view class="zk-des">
    <des-item
      :schema="schema"
      :data="data"
      v-for="schema in schemas"
      :key="schema.field"
    ></des-item>
  </view>
</template>

<style scoped lang="scss">
.zk-des {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
</style>
