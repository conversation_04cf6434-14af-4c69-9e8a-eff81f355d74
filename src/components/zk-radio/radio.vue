<script setup lang="ts">
import { useRuleFormItem } from "@/hook/useFormItem";
type ValueState = string | number;
interface Option {
  label: string;
  value: ValueState;
}
interface Props {
  value?: ValueState;
  options?: Option[];
}
const props = withDefaults(defineProps<Props>(), {
  value: undefined,
  options: () => [],
});
const emit = defineEmits(["update:value", "change"]);

const [state] = useRuleFormItem<Props, keyof Props, ValueState>(
  props,
  "value",
  ["update:value", "change"],
);
</script>

<template>
  <view class="zk-radio">
    <view
      class="zk-radio-item common-tr def"
      v-for="item in options"
      :key="item.value"
      @click="() => (state = item.value)"
    >
      <view>
        {{ item.label }}
      </view>
      <view
        class="icon"
        :class="{
          'icon-action': item.value === state,
          'icon-def': item.value != state,
        }"
      >
        <nut-icon
          v-if="item.value === state"
          name="checked"
          size="40rpx"
        ></nut-icon>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.zk-radio {
  display: flex;
  justify-content: space-between;
  gap: 40rpx;
  padding: 100rpx 0;
  &-item {
    flex-grow: 1;
    width: 0;
    height: 120rpx;
    flex-shrink: 0;
    border-radius: 100rpx;
    font-size: 36rpx;
    text-align: center;
    display: flex;
    align-items: center;
    padding: 0 40rpx;
    justify-content: space-between;

    &.def {
      color: #333;
      background: #f1f5fb;
    }
    &.action {
      background: #e9f0fe;
      color: #1677ff;
    }
    & .icon {
      display: flex;
      align-items: center;
      background: #fff;
      border-radius: 50%;
      width: 40rpx;
      height: 40rpx;
      border: 1rpx solid transparent;
    }
    & .icon-def {
      background: #fff;
      border: 1rpx solid #cccccc;
    }
    & .icon-action {
      color: #1677ff;
    }
  }
}
</style>
